/**
 * Client-Side Logger System
 * Provides comprehensive logging functionality for client-side operations
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    SUCCESS = 4
}
export interface LogEntry {
    timestamp: Date;
    level: LogLevel;
    message: string;
    data?: any;
    source?: string;
}
export declare class Logger {
    private static logLevel;
    private static logs;
    private static maxLogs;
    private static enableConsole;
    /**
     * Set the minimum log level
     */
    static setLogLevel(level: LogLevel): void;
    /**
     * Enable/disable console output
     */
    static setConsoleOutput(enabled: boolean): void;
    /**
     * Debug level logging
     */
    static debug(message: string, data?: any, source?: string): void;
    /**
     * Info level logging
     */
    static info(message: string, data?: any, source?: string): void;
    /**
     * Warning level logging
     */
    static warn(message: string, data?: any, source?: string): void;
    /**
     * Error level logging
     */
    static error(message: string, data?: any, source?: string): void;
    /**
     * Success level logging
     */
    static success(message: string, data?: any, source?: string): void;
    /**
     * Core logging method
     */
    private static log;
    /**
     * Output log to browser console
     */
    private static outputToConsole;
    /**
     * Send log to server (optional)
     */
    private static sendToServer;
    /**
     * Get level string
     */
    private static getLevelString;
    /**
     * Get all logs
     */
    static getLogs(): LogEntry[];
    /**
     * Get logs by level
     */
    static getLogsByLevel(level: LogLevel): LogEntry[];
    /**
     * Clear all logs
     */
    static clearLogs(): void;
    /**
     * Export logs to string
     */
    static exportLogs(): string;
    /**
     * Get logs for UI display
     */
    static getLogsForUI(limit?: number): LogEntry[];
    /**
     * Filter logs by search term
     */
    static searchLogs(searchTerm: string): LogEntry[];
    /**
     * Get log statistics
     */
    static getLogStats(): {
        [key: string]: number;
    };
}
//# sourceMappingURL=Logger.d.ts.map