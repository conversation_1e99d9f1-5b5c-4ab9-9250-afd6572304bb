/**
 * UI Context Provider
 * Provides UI state and actions to React components
 */
import React, { ReactNode } from 'react';
import { NotificationData } from '../UIManager';
export interface UIState {
    isLoading: boolean;
    currentScreen: 'loading' | 'auth' | 'character-selection' | 'game';
    notifications: NotificationData[];
    modals: string[];
    hudVisible: boolean;
    chatVisible: boolean;
    cursorVisible: boolean;
    characterData: any;
    userData: any;
}
export interface UIActions {
    setLoading: (loading: boolean) => void;
    setCurrentScreen: (screen: UIState['currentScreen']) => void;
    addNotification: (notification: NotificationData) => void;
    removeNotification: (id: string) => void;
    clearNotifications: () => void;
    openModal: (modalName: string) => void;
    closeModal: (modalName: string) => void;
    setHUDVisible: (visible: boolean) => void;
    setChatVisible: (visible: boolean) => void;
    setCursorVisible: (visible: boolean) => void;
    setCharacterData: (data: any) => void;
    setUserData: (data: any) => void;
}
declare const UIContext: React.Context<{
    state: UIState;
    actions: UIActions;
} | null>;
export declare const UIProvider: React.FC<{
    children: ReactNode;
}>;
export declare const useUI: () => {
    state: UIState;
    actions: UIActions;
};
export default UIContext;
//# sourceMappingURL=UIContext.d.ts.map