/**
 * Audio Manager
 * Handles audio operations and sound management
 */

import { Logger } from '../utils/Logger';
import { EventManager } from '../events/EventManager';

export interface AudioSettings {
    masterVolume: number;
    musicVolume: number;
    sfxVolume: number;
    voiceVolume: number;
    ambientVolume: number;
}

export interface SoundEffect {
    id: string;
    url: string;
    volume: number;
    loop: boolean;
    category: 'music' | 'sfx' | 'voice' | 'ambient';
}

export class AudioManager {
    private eventManager: EventManager;
    private audioSettings: AudioSettings;
    private activeSounds: Map<string, HTMLAudioElement> = new Map();
    private audioContext: AudioContext | null = null;

    constructor(eventManager: EventManager) {
        this.eventManager = eventManager;
        this.audioSettings = {
            masterVolume: 1.0,
            musicVolume: 0.8,
            sfxVolume: 0.9,
            voiceVolume: 1.0,
            ambientVolume: 0.6
        };
    }

    /**
     * Initialize Audio Manager
     */
    public async initialize(): Promise<void> {
        try {
            Logger.info('🔊 Initializing Audio Manager...');

            // Initialize Web Audio API
            this.initializeAudioContext();

            // Register audio events
            this.registerEvents();

            // Load audio settings from storage
            this.loadAudioSettings();

            Logger.success('✅ Audio Manager initialized');
        } catch (error) {
            Logger.error('❌ Failed to initialize Audio Manager:', error);
            throw error;
        }
    }

    /**
     * Shutdown Audio Manager
     */
    public async shutdown(): Promise<void> {
        try {
            Logger.info('🛑 Shutting down Audio Manager...');
            
            // Stop all active sounds
            this.stopAllSounds();
            
            // Close audio context
            if (this.audioContext) {
                await this.audioContext.close();
                this.audioContext = null;
            }
            
            Logger.success('✅ Audio Manager shut down');
        } catch (error) {
            Logger.error('❌ Error shutting down Audio Manager:', error);
        }
    }

    /**
     * Initialize Web Audio API context
     */
    private initializeAudioContext(): void {
        try {
            this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
            Logger.debug('Audio context initialized');
        } catch (error) {
            Logger.warn('Failed to initialize audio context:', error);
        }
    }

    /**
     * Register audio events
     */
    private registerEvents(): void {
        this.eventManager.on('audio:playSound', this.onPlaySound.bind(this));
        this.eventManager.on('audio:stopSound', this.onStopSound.bind(this));
        this.eventManager.on('audio:setVolume', this.onSetVolume.bind(this));
        this.eventManager.on('audio:playMusic', this.onPlayMusic.bind(this));
        this.eventManager.on('audio:stopMusic', this.onStopMusic.bind(this));

        Logger.debug('Audio events registered');
    }

    /**
     * Play a sound effect
     */
    public playSound(soundId: string, url: string, volume: number = 1.0, loop: boolean = false): void {
        try {
            // Stop existing sound with same ID
            if (this.activeSounds.has(soundId)) {
                this.stopSound(soundId);
            }

            const audio = new Audio(url);
            audio.volume = volume * this.audioSettings.sfxVolume * this.audioSettings.masterVolume;
            audio.loop = loop;

            audio.addEventListener('ended', () => {
                if (!loop) {
                    this.activeSounds.delete(soundId);
                }
            });

            audio.addEventListener('error', (error) => {
                Logger.error(`Failed to load audio: ${url}`, error);
                this.activeSounds.delete(soundId);
            });

            this.activeSounds.set(soundId, audio);
            audio.play().catch(error => {
                Logger.error(`Failed to play audio: ${url}`, error);
            });

            Logger.debug(`Playing sound: ${soundId}`);
        } catch (error) {
            Logger.error(`Error playing sound ${soundId}:`, error);
        }
    }

    /**
     * Stop a sound effect
     */
    public stopSound(soundId: string): void {
        const audio = this.activeSounds.get(soundId);
        if (audio) {
            audio.pause();
            audio.currentTime = 0;
            this.activeSounds.delete(soundId);
            Logger.debug(`Stopped sound: ${soundId}`);
        }
    }

    /**
     * Stop all sounds
     */
    public stopAllSounds(): void {
        this.activeSounds.forEach((audio, soundId) => {
            audio.pause();
            audio.currentTime = 0;
        });
        this.activeSounds.clear();
        Logger.debug('All sounds stopped');
    }

    /**
     * Set volume for a category
     */
    public setVolume(category: keyof AudioSettings, volume: number): void {
        volume = Math.max(0, Math.min(1, volume)); // Clamp between 0 and 1
        this.audioSettings[category] = volume;

        // Update all active sounds of this category
        this.updateActiveSoundsVolume();

        // Save settings
        this.saveAudioSettings();

        Logger.debug(`${category} volume set to: ${volume}`);
    }

    /**
     * Get current volume for a category
     */
    public getVolume(category: keyof AudioSettings): number {
        return this.audioSettings[category];
    }

    /**
     * Update volume for all active sounds
     */
    private updateActiveSoundsVolume(): void {
        this.activeSounds.forEach((audio, soundId) => {
            // This is a simplified approach - in a real implementation,
            // you'd track the category of each sound
            audio.volume = this.audioSettings.sfxVolume * this.audioSettings.masterVolume;
        });
    }

    /**
     * Load audio settings from localStorage
     */
    private loadAudioSettings(): void {
        try {
            const savedSettings = localStorage.getItem('audioSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                this.audioSettings = { ...this.audioSettings, ...settings };
                Logger.debug('Audio settings loaded from storage');
            }
        } catch (error) {
            Logger.warn('Failed to load audio settings:', error);
        }
    }

    /**
     * Save audio settings to localStorage
     */
    private saveAudioSettings(): void {
        try {
            localStorage.setItem('audioSettings', JSON.stringify(this.audioSettings));
            Logger.debug('Audio settings saved to storage');
        } catch (error) {
            Logger.warn('Failed to save audio settings:', error);
        }
    }

    /**
     * Event handlers
     */
    private onPlaySound(data: { soundId: string; url: string; volume?: number; loop?: boolean }): void {
        this.playSound(data.soundId, data.url, data.volume, data.loop);
    }

    private onStopSound(data: { soundId: string }): void {
        this.stopSound(data.soundId);
    }

    private onSetVolume(data: { category: keyof AudioSettings; volume: number }): void {
        this.setVolume(data.category, data.volume);
    }

    private onPlayMusic(data: { url: string; volume?: number; loop?: boolean }): void {
        this.playSound('background_music', data.url, data.volume || 0.8, data.loop !== false);
    }

    private onStopMusic(): void {
        this.stopSound('background_music');
    }

    /**
     * Get current audio settings
     */
    public getAudioSettings(): AudioSettings {
        return { ...this.audioSettings };
    }

    /**
     * Check if sound is playing
     */
    public isSoundPlaying(soundId: string): boolean {
        const audio = this.activeSounds.get(soundId);
        return audio ? !audio.paused : false;
    }

    /**
     * Get active sounds count
     */
    public getActiveSoundsCount(): number {
        return this.activeSounds.size;
    }
}
