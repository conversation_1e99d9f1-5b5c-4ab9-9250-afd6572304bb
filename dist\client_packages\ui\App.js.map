{"version": 3, "file": "App.js", "sourceRoot": "", "sources": ["../../../client_packages/ui/App.tsx"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAmD;AACnD,mDAAiD;AACjD,yDAAuD;AACvD,wEAAqE;AACrE,8CAA2C;AAC3C,yFAAsF;AACtF,kFAA+E;AAC/E,8DAA2D;AAC3D,8DAA2D;AAC3D,+BAA6B;AAUtB,MAAM,GAAG,GAAa,GAAG,EAAE;IAC9B,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,IAAA,gBAAQ,EAAW;QAC/C,SAAS,EAAE,IAAI;QACf,eAAe,EAAE,KAAK;QACtB,YAAY,EAAE,KAAK;QACnB,SAAS,EAAE,KAAK;QAChB,aAAa,EAAE,SAAS;KAC3B,CAAC,CAAC;IAEH,IAAA,iBAAS,EAAC,GAAG,EAAE;QACX,iBAAiB;QACjB,aAAa,EAAE,CAAC;QAEhB,4BAA4B;QAC5B,IAAI,OAAO,EAAE,KAAK,WAAW,EAAE,CAAC;YAC5B,wBAAwB;YACxB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,EAAE;gBAC/B,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE,GAAG,EAAE;gBAC5C,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjB,GAAG,IAAI;oBACP,aAAa,EAAE,qBAAqB;oBACpC,eAAe,EAAE,IAAI;iBACxB,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,EAAE;gBAC/B,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACjB,GAAG,IAAI;oBACP,aAAa,EAAE,MAAM;oBACrB,YAAY,EAAE,IAAI;iBACrB,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,EAAE;gBACjC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;QACP,CAAC;QAED,OAAO,GAAG,EAAE;YACR,0BAA0B;YAC1B,IAAI,OAAO,EAAE,KAAK,WAAW,EAAE,CAAC;gBAC5B,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBACjC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;gBAC9C,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBACjC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACvC,CAAC;QACL,CAAC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAG,KAAK,IAAmB,EAAE;QAC5C,IAAI,CAAC;YACD,gCAAgC;YAChC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,uCAAuC;YACvC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjB,GAAG,IAAI;gBACP,SAAS,EAAE,KAAK;gBAChB,aAAa,EAAE,MAAM;aACxB,CAAC,CAAC,CAAC;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,GAAoB,EAAE;QAC9C,QAAQ,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC7B,KAAK,SAAS;gBACV,OAAO,CAAC,6BAAa,CAAC,AAAD,EAAG,CAAC;YAE7B,KAAK,MAAM;gBACP,OAAO,CACH,CAAC,yCAAmB,CAChB,MAAM,CAAC,CAAC,IAAI,CAAC,CACb,eAAe,CAAC,CAAC,GAAG,EAAE;wBAClB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BACjB,GAAG,IAAI;4BACP,eAAe,EAAE,IAAI;4BACrB,aAAa,EAAE,qBAAqB;yBACvC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,EACJ,CACL,CAAC;YAEN,KAAK,qBAAqB;gBACtB,OAAO,CACH,CAAC,uCAAkB,CACf,mBAAmB,CAAC,CAAC,GAAG,EAAE;wBACtB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BACjB,GAAG,IAAI;4BACP,YAAY,EAAE,IAAI;4BAClB,aAAa,EAAE,MAAM;yBACxB,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,EACJ,CACL,CAAC;YAEN,KAAK,MAAM;gBACP,OAAO,CAAC,SAAG,CAAC,AAAD,EAAG,CAAC;YAEnB;gBACI,OAAO,CAAC,6BAAa,CAAC,AAAD,EAAG,CAAC;QACjC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,CACH,CAAC,4BAAa,CACV;YAAA,CAAC,sBAAU,CACP;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAC1B;oBAAA,CAAC,qBAAqB,CACtB;oBAAA,CAAC,mBAAmB,EAAE,CAEtB;;oBAAA,CAAC,+BAA+B,CAChC;oBAAA,CAAC,uCAAkB,CAAC,AAAD,EAEnB;;oBAAA,CAAC,uDAAuD,CACxD;oBAAA,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,uBAAU,CAAC,AAAD,EAAG,CAErC;;oBAAA,CAAC,qBAAqB,CACtB;oBAAA,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,EACpB;oBAAA,CAAC,GAAG,CAAC,EAAE,CAAC,cAAc,EAC1B;gBAAA,EAAE,GAAG,CACT;YAAA,EAAE,sBAAU,CAChB;QAAA,EAAE,4BAAa,CAAC,CACnB,CAAC;AACN,CAAC,CAAC;AAhIW,QAAA,GAAG,OAgId;AAEF,kBAAe,WAAG,CAAC"}