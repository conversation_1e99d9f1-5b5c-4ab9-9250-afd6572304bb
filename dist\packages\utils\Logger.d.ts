/**
 * Advanced Logger System
 * Provides comprehensive logging functionality with different levels and formatting
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    SUCCESS = 4
}
export interface LogEntry {
    timestamp: Date;
    level: LogLevel;
    message: string;
    data?: any;
    source?: string;
}
export declare class Logger {
    private static logLevel;
    private static logs;
    private static maxLogs;
    /**
     * Set the minimum log level
     */
    static setLogLevel(level: LogLevel): void;
    /**
     * Debug level logging
     */
    static debug(message: string, data?: any, source?: string): void;
    /**
     * Info level logging
     */
    static info(message: string, data?: any, source?: string): void;
    /**
     * Warning level logging
     */
    static warn(message: string, data?: any, source?: string): void;
    /**
     * Error level logging
     */
    static error(message: string, data?: any, source?: string): void;
    /**
     * Success level logging
     */
    static success(message: string, data?: any, source?: string): void;
    /**
     * Core logging method
     */
    private static log;
    /**
     * Format log message for console output
     */
    private static formatMessage;
    /**
     * Get colored level string
     */
    private static getLevelString;
    /**
     * Get all logs
     */
    static getLogs(): LogEntry[];
    /**
     * Get logs by level
     */
    static getLogsByLevel(level: LogLevel): LogEntry[];
    /**
     * Clear all logs
     */
    static clearLogs(): void;
    /**
     * Export logs to string
     */
    static exportLogs(): string;
}
//# sourceMappingURL=Logger.d.ts.map