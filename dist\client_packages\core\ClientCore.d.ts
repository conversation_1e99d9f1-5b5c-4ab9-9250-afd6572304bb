/**
 * Client Core - Main client management class
 * Handles initialization, module loading, and client lifecycle
 */
import { EventManager } from '../events/EventManager';
import { UIManager } from '../ui/UIManager';
import { InputManager } from '../input/InputManager';
import { CameraManager } from '../camera/CameraManager';
import { AudioManager } from '../audio/AudioManager';
import { NetworkManager } from '../network/NetworkManager';
import { ConfigManager } from '../config/ConfigManager';
export declare class ClientCore {
    private static instance;
    private isInitialized;
    private isRunning;
    private eventManager;
    private uiManager;
    private inputManager;
    private cameraManager;
    private audioManager;
    private networkManager;
    private configManager;
    constructor();
    /**
     * Initialize all client components
     */
    start(): Promise<void>;
    /**
     * Shutdown client gracefully
     */
    shutdown(): Promise<void>;
    /**
     * Initialize all managers
     */
    private initializeManagers;
    /**
     * Shutdown all managers
     */
    private shutdownManagers;
    /**
     * Register all client events
     */
    private registerEvents;
    /**
     * Handle render event
     */
    private onRender;
    /**
     * Handle browser created
     */
    private onBrowserCreated;
    /**
     * Handle browser loading failed
     */
    private onBrowserLoadingFailed;
    /**
     * Handle notification
     */
    private onNotification;
    /**
     * Handle show login form
     */
    private onShowLoginForm;
    /**
     * Handle show register form
     */
    private onShowRegisterForm;
    /**
     * Handle authentication error
     */
    private onAuthError;
    /**
     * Handle show character selection
     */
    private onShowCharacterSelection;
    /**
     * Handle character loaded
     */
    private onCharacterLoaded;
    /**
     * Handle character error
     */
    private onCharacterError;
    /**
     * Handle apply skin data
     */
    private onApplySkinData;
    /**
     * Handle apply clothes
     */
    private onApplyClothes;
    static getInstance(): ClientCore;
    getEventManager(): EventManager;
    getUIManager(): UIManager;
    getInputManager(): InputManager;
    getCameraManager(): CameraManager;
    getAudioManager(): AudioManager;
    getNetworkManager(): NetworkManager;
    getConfigManager(): ConfigManager;
    /**
     * Check if client is running
     */
    isClientRunning(): boolean;
    /**
     * Check if client is initialized
     */
    isClientInitialized(): boolean;
}
//# sourceMappingURL=ClientCore.d.ts.map