/**
 * Main React Application Component
 * Root component for the entire UI system
 */
import React from 'react';
import './styles/global.css';
export interface AppState {
    isLoading: boolean;
    isAuthenticated: boolean;
    hasCharacter: boolean;
    showDebug: boolean;
    currentScreen: 'loading' | 'auth' | 'character-selection' | 'game';
}
export declare const App: React.FC;
export default App;
//# sourceMappingURL=App.d.ts.map