/**
 * Event Manager
 * Advanced event handling system with middleware support
 */

import { Logger } from '../utils/Logger';

export type EventCallback = (...args: any[]) => void | Promise<void>;
export type EventMiddleware = (eventName: string, args: any[], next: () => void) => void | Promise<void>;

export interface EventListener {
    callback: EventCallback;
    once: boolean;
    priority: number;
}

export class EventManager {
    private events: Map<string, EventListener[]> = new Map();
    private middlewares: EventMiddleware[] = [];
    private eventHistory: Array<{ name: string; args: any[]; timestamp: Date }> = [];
    private maxHistorySize: number = 1000;

    /**
     * Add an event listener
     */
    public on(eventName: string, callback: EventCallback, priority: number = 0): void {
        this.addEventListener(eventName, callback, false, priority);
    }

    /**
     * Add a one-time event listener
     */
    public once(eventName: string, callback: EventCallback, priority: number = 0): void {
        this.addEventListener(eventName, callback, true, priority);
    }

    /**
     * Remove an event listener
     */
    public off(eventName: string, callback?: EventCallback): void {
        const listeners = this.events.get(eventName);
        if (!listeners) return;

        if (callback) {
            const index = listeners.findIndex(listener => listener.callback === callback);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        } else {
            this.events.delete(eventName);
        }

        Logger.debug(`Event listener removed: ${eventName}`);
    }

    /**
     * Remove all listeners for an event
     */
    public removeAllListeners(eventName?: string): void {
        if (eventName) {
            this.events.delete(eventName);
            Logger.debug(`All listeners removed for event: ${eventName}`);
        } else {
            this.events.clear();
            Logger.debug('All event listeners removed');
        }
    }

    /**
     * Emit an event
     */
    public async emit(eventName: string, ...args: any[]): Promise<void> {
        try {
            // Add to history
            this.addToHistory(eventName, args);

            // Apply middlewares
            await this.applyMiddlewares(eventName, args);

            // Get listeners
            const listeners = this.events.get(eventName);
            if (!listeners || listeners.length === 0) {
                Logger.debug(`No listeners for event: ${eventName}`);
                return;
            }

            // Sort by priority (higher priority first)
            const sortedListeners = [...listeners].sort((a, b) => b.priority - a.priority);

            // Execute listeners
            for (const listener of sortedListeners) {
                try {
                    await listener.callback(...args);

                    // Remove if it's a one-time listener
                    if (listener.once) {
                        const index = listeners.indexOf(listener);
                        if (index !== -1) {
                            listeners.splice(index, 1);
                        }
                    }
                } catch (error) {
                    Logger.error(`Error in event listener for ${eventName}:`, error);
                }
            }

            Logger.debug(`Event emitted: ${eventName}`, { args });
        } catch (error) {
            Logger.error(`Error emitting event ${eventName}:`, error);
        }
    }

    /**
     * Add middleware
     */
    public use(middleware: EventMiddleware): void {
        this.middlewares.push(middleware);
        Logger.debug('Event middleware added');
    }

    /**
     * Remove middleware
     */
    public removeMiddleware(middleware: EventMiddleware): void {
        const index = this.middlewares.indexOf(middleware);
        if (index !== -1) {
            this.middlewares.splice(index, 1);
            Logger.debug('Event middleware removed');
        }
    }

    /**
     * Get event listeners count
     */
    public getListenerCount(eventName: string): number {
        const listeners = this.events.get(eventName);
        return listeners ? listeners.length : 0;
    }

    /**
     * Get all event names
     */
    public getEventNames(): string[] {
        return Array.from(this.events.keys());
    }

    /**
     * Check if event has listeners
     */
    public hasListeners(eventName: string): boolean {
        return this.getListenerCount(eventName) > 0;
    }

    /**
     * Get event history
     */
    public getEventHistory(): Array<{ name: string; args: any[]; timestamp: Date }> {
        return [...this.eventHistory];
    }

    /**
     * Clear event history
     */
    public clearEventHistory(): void {
        this.eventHistory = [];
        Logger.debug('Event history cleared');
    }

    /**
     * Wait for an event to be emitted
     */
    public waitFor(eventName: string, timeout?: number): Promise<any[]> {
        return new Promise((resolve, reject) => {
            let timeoutId: NodeJS.Timeout | undefined;

            const listener = (...args: any[]) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                this.off(eventName, listener);
                resolve(args);
            };

            this.once(eventName, listener);

            if (timeout) {
                timeoutId = setTimeout(() => {
                    this.off(eventName, listener);
                    reject(new Error(`Event ${eventName} timeout after ${timeout}ms`));
                }, timeout);
            }
        });
    }

    /**
     * Create a namespaced event emitter
     */
    public namespace(prefix: string): NamespacedEventManager {
        return new NamespacedEventManager(this, prefix);
    }

    /**
     * Add event listener with options
     */
    private addEventListener(eventName: string, callback: EventCallback, once: boolean, priority: number): void {
        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }

        const listeners = this.events.get(eventName)!;
        listeners.push({
            callback,
            once,
            priority
        });

        Logger.debug(`Event listener added: ${eventName}`, { once, priority });
    }

    /**
     * Apply middlewares
     */
    private async applyMiddlewares(eventName: string, args: any[]): Promise<void> {
        for (const middleware of this.middlewares) {
            await new Promise<void>((resolve, reject) => {
                try {
                    const next = () => resolve();
                    const result = middleware(eventName, args, next);
                    
                    // Handle async middleware
                    if (result instanceof Promise) {
                        result.then(() => resolve()).catch(reject);
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }
    }

    /**
     * Add event to history
     */
    private addToHistory(eventName: string, args: any[]): void {
        this.eventHistory.push({
            name: eventName,
            args: [...args],
            timestamp: new Date()
        });

        // Maintain history size limit
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }
}

/**
 * Namespaced Event Manager
 * Provides scoped event handling
 */
export class NamespacedEventManager {
    constructor(
        private eventManager: EventManager,
        private prefix: string
    ) {}

    public on(eventName: string, callback: EventCallback, priority: number = 0): void {
        this.eventManager.on(`${this.prefix}:${eventName}`, callback, priority);
    }

    public once(eventName: string, callback: EventCallback, priority: number = 0): void {
        this.eventManager.once(`${this.prefix}:${eventName}`, callback, priority);
    }

    public off(eventName: string, callback?: EventCallback): void {
        this.eventManager.off(`${this.prefix}:${eventName}`, callback);
    }

    public async emit(eventName: string, ...args: any[]): Promise<void> {
        await this.eventManager.emit(`${this.prefix}:${eventName}`, ...args);
    }

    public getListenerCount(eventName: string): number {
        return this.eventManager.getListenerCount(`${this.prefix}:${eventName}`);
    }

    public hasListeners(eventName: string): boolean {
        return this.eventManager.hasListeners(`${this.prefix}:${eventName}`);
    }

    public waitFor(eventName: string, timeout?: number): Promise<any[]> {
        return this.eventManager.waitFor(`${this.prefix}:${eventName}`, timeout);
    }
}
