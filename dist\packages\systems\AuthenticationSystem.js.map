{"version": 3, "file": "AuthenticationSystem.js", "sourceRoot": "", "sources": ["../../../packages/systems/AuthenticationSystem.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AACzC,iEAA8D;AAC9D,yDAAsD;AAetD,MAAa,oBAAoB;IAM7B;QAHQ,qBAAgB,GAAW,CAAC,CAAC;QAC7B,oBAAe,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,aAAa;QAG3D,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,eAAgC,EAAE,YAA0B;QAC/E,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAAC,MAAsB;QAC/C,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,8BAA8B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAEzD,yCAAyC;YACzC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAE1D,IAAI,YAAY,EAAE,CAAC;gBACf,kBAAkB;gBAClB,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACJ,yBAAyB;gBACzB,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC3C,CAAC;YAED,yCAAyC;YACzC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC;QAE3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,MAAsB,EAAE,IAAe;QAC5D,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAEtE,iBAAiB;YACjB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;gBACjD,OAAO;YACX,CAAC;YAED,gCAAgC;YAChC,IAAI,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,4DAA4D,CAAC,CAAC;gBACzF,OAAO;YACX,CAAC;YAED,yBAAyB;YACzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,8BAA8B,CAAC,CAAC;gBACrE,OAAO;YACX,CAAC;YAED,kBAAkB;YAClB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrF,IAAI,CAAC,eAAe,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,8BAA8B,CAAC,CAAC;gBACrE,OAAO;YACX,CAAC;YAED,0BAA0B;YAC1B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,mBAAmB,IAAI,CAAC,UAAU,IAAI,qBAAqB,EAAE,CAAC,CAAC;gBAC1F,OAAO;YACX,CAAC;YAED,mBAAmB;YACnB,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,mBAAmB,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,kCAAkC,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CAAC,MAAsB,EAAE,IAAkB;QAClE,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,gCAAgC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE7E,iBAAiB;YACjB,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACxD,IAAI,eAAe,EAAE,CAAC;gBAClB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAC5C,OAAO;YACX,CAAC;YAED,mCAAmC;YACnC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjE,IAAI,YAAY,EAAE,CAAC;gBACf,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC;gBACtD,OAAO;YACX,CAAC;YAED,gCAAgC;YAChC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5D,IAAI,aAAa,EAAE,CAAC;gBAChB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;gBACvD,OAAO;YACX,CAAC;YAED,sCAAsC;YACtC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC7E,IAAI,kBAAkB,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,gDAAgD,CAAC,CAAC;gBAC7E,OAAO;YACX,CAAC;YAED,gBAAgB;YAChB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE5D,sBAAsB;YACtB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACxE,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;gBACvD,OAAO;YACX,CAAC;YAED,iBAAiB;YACjB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,6BAA6B,CAAC,CAAC;gBAC1D,OAAO;YACX,CAAC;YAED,oCAAoC;YACpC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAElD,eAAM,CAAC,OAAO,CAAC,0BAA0B,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAE/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,0BAA0B,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,yCAAyC,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,MAAsB,EAAE,IAAS;QACjE,IAAI,CAAC;YACD,qBAAqB;YACrB,MAAM,CAAC,QAAQ,GAAG;gBACd,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,UAAU,EAAE,IAAI,CAAC,WAAW;gBAC5B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;gBACxC,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,UAAU,EAAE,IAAI,CAAC,WAAW;gBAC5B,aAAa,EAAE,IAAI,CAAC,cAAc;aACrC,CAAC;YAEF,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;YACzB,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;YAEzB,kBAAkB;YAClB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC9B,2GAA2G,EAC3G,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CACvB,CAAC;YAEF,yBAAyB;YACzB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;YAErB,2BAA2B;YAC3B,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAE1C,mBAAmB;YACnB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAErD,eAAM,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAE5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAsB,EAAE,OAAe;QACnE,MAAM,CAAC,aAAa,EAAE,CAAC;QAEvB,kBAAkB;QAClB,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB;YAC7D,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;YAC7C,CAAC,CAAC,IAAI,CAAC;QAEX,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC9B,oEAAoE,EACpE,CAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAC1D,CAAC;QACN,CAAC;QAED,qBAAqB;QACrB,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChD,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,0DAA0D,CAAC,CAAC;QAC3F,CAAC;aAAM,CAAC;YACJ,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,aAAa,CAAC;YAClE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,OAAO,KAAK,YAAY,sBAAsB,CAAC,CAAC;QAClF,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;IAC3G,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,MAAsB;QACvD,IAAI,CAAC,MAAM,CAAC,QAAQ;YAAE,OAAO;QAE7B,IAAI,CAAC;YACD,0BAA0B;YAC1B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CACjD,iEAAiE,EACjE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CACvB,CAAC;YAEF,gCAAgC;YAChC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC;QAErE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,0CAA0C,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAsB;QAClD,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C,gEAAgE,EAChE,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,CACrC,CAAC;YAEF,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAe;QACrC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;YAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,EAAE;YACvD,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,IAAkB;QAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC1E,OAAO,8CAA8C,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,OAAO,6DAA6D,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAChE,OAAO,uBAAuB,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7C,OAAO,6CAA6C,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;YACzC,OAAO,wBAAwB,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAsB;QAClD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAAE,OAAO,KAAK,CAAC;QAEvC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C,6CAA6C,EAC7C,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CACvB,CAAC;YAEF,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;gBACxD,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACzD,CAAC;YAED,OAAO,KAAK,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C,wCAAwC,EACxC,CAAC,QAAQ,CAAC,CACb,CAAC;YAEF,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,KAAa;QACtC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C,qCAAqC,EACrC,CAAC,KAAK,CAAC,CACV,CAAC;YAEF,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,UAAkB;QAChD,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C,2CAA2C,EAC3C,CAAC,UAAU,CAAC,CACf,CAAC;YAEF,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,EAAU;QAChC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C,kCAAkC,EAClC,CAAC,EAAE,CAAC,CACP,CAAC;YAEF,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,QAAgB;QACvC,iCAAiC;QACjC,8BAA8B;QAC9B,OAAO,UAAU,QAAQ,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,IAAY;QACvD,sCAAsC;QACtC,6BAA6B;QAC7B,OAAO,IAAI,KAAK,UAAU,QAAQ,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAsB,EAAE,IAAkB,EAAE,YAAoB;QAC5F,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C;kDACkC,EAClC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,CACzF,CAAC;YAEF,OAAO,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,MAAsB,EAAE,OAAe;QACzD,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3C,eAAM,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;CACJ;AAzbD,oDAybC"}