{"version": 3, "file": "ServerCore.js", "sourceRoot": "", "sources": ["../../../packages/core/ServerCore.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AACzC,2DAAwD;AACxD,iEAA8D;AAC9D,yDAAsD;AACtD,6DAA0D;AAC1D,+DAA4D;AAC5D,0EAAuE;AACvE,gEAA6D;AAE7D,MAAa,UAAU;IAgBnB;QAdQ,kBAAa,GAAY,KAAK,CAAC;QAC/B,cAAS,GAAY,KAAK,CAAC;QAc/B,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO,UAAU,CAAC,QAAQ,CAAC;QAC/B,CAAC;QACD,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,KAAK;QACd,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAE9C,2BAA2B;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAEhC,sBAAsB;YACtB,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAErC,0BAA0B;YAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;YAEvC,sBAAsB;YACtB,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;YACzC,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;YAE3C,qBAAqB;YACrB,IAAI,CAAC,UAAU,GAAG,IAAI,2CAAoB,EAAE,CAAC;YAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;YAE7C,sBAAsB;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;YAEtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,eAAM,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAE/C,IAAI,CAAC;YACD,uBAAuB;YACvB,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAE1C,6BAA6B;YAC7B,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;YAExC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,eAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,gBAAgB;QAChB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5D,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5D,gBAAgB;QAChB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACnE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,MAAgB;QACvC,eAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,oBAAoB,CAAC,CAAC;QAC1D,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,MAAgB,EAAE,QAAgB,EAAE,MAAc;QACzE,eAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,qBAAqB,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC;QACjF,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,MAAgB;QACxC,eAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,IAAI,WAAW,CAAC,CAAC;QAChD,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,MAAgB,EAAE,MAAc,EAAE,MAAgB;QAC1E,eAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC;QAC7C,0BAA0B;IAC9B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,MAAgB,EAAE,IAAS;QACnD,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAAgB,EAAE,IAAS;QACtD,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,6CAA6C;IACtC,MAAM,CAAC,WAAW;QACrB,OAAO,UAAU,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAEM,gBAAgB;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEM,kBAAkB;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAEM,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEM,gBAAgB;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEM,iBAAiB;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEM,aAAa;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEM,kBAAkB;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;CACJ;AAjLD,gCAiLC"}