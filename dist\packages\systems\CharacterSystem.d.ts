/**
 * Character System
 * Handles character creation, selection, and management
 */
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';
import { ExtendedPlayer } from '../managers/PlayerManager';
export interface CharacterCreationData {
    name: string;
    age: number;
    gender: 'male' | 'female';
    skinData: any;
    clothesData: any;
}
export declare class CharacterSystem {
    private databaseManager;
    private eventManager;
    private maxCharacters;
    constructor();
    /**
     * Set dependencies (called by ServerCore)
     */
    setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void;
    /**
     * Handle character selection
     */
    selectCharacter(player: ExtendedPlayer, characterId: number): Promise<void>;
    /**
     * Handle character creation
     */
    createCharacter(player: ExtendedPlayer, data: CharacterCreationData): Promise<void>;
    /**
     * Handle character deletion
     */
    deleteCharacter(player: ExtendedPlayer, characterId: number): Promise<void>;
    /**
     * Load character data and spawn player
     */
    private loadCharacter;
    /**
     * Apply character appearance
     */
    private applyCharacterAppearance;
    /**
     * Show character selection screen
     */
    showCharacterSelection(player: ExtendedPlayer): Promise<void>;
    /**
     * Get character by ID
     */
    private getCharacterById;
    /**
     * Get character by name
     */
    private getCharacterByName;
    /**
     * Get character count for user
     */
    private getCharacterCount;
    /**
     * Create character in database
     */
    private createCharacterInDatabase;
    /**
     * Validate character creation data
     */
    private validateCharacterData;
    /**
     * Send character error to player
     */
    private sendCharacterError;
    /**
     * Save character data
     */
    saveCharacterData(player: ExtendedPlayer): Promise<void>;
}
//# sourceMappingURL=CharacterSystem.d.ts.map