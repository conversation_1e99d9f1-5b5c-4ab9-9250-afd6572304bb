/**
 * Main React Application Component
 * Root component for the entire UI system
 */

import React, { useState, useEffect } from 'react';
import { UIProvider } from './context/UIContext';
import { ThemeProvider } from './context/ThemeContext';
import { NotificationSystem } from './components/NotificationSystem';
import { HUD } from './components/HUD/HUD';
import { AuthenticationModal } from './components/Authentication/AuthenticationModal';
import { CharacterSelection } from './components/Character/CharacterSelection';
import { LoadingScreen } from './components/LoadingScreen';
import { DebugPanel } from './components/Debug/DebugPanel';
import './styles/global.css';

export interface AppState {
    isLoading: boolean;
    isAuthenticated: boolean;
    hasCharacter: boolean;
    showDebug: boolean;
    currentScreen: 'loading' | 'auth' | 'character-selection' | 'game';
}

export const App: React.FC = () => {
    const [appState, setAppState] = useState<AppState>({
        isLoading: true,
        isAuthenticated: false,
        hasCharacter: false,
        showDebug: false,
        currentScreen: 'loading'
    });

    useEffect(() => {
        // Initialize app
        initializeApp();

        // Listen for RAGE MP events
        if (typeof mp !== 'undefined') {
            // Authentication events
            mp.events.add('ui:showLogin', () => {
                setAppState(prev => ({ ...prev, currentScreen: 'auth', isLoading: false }));
            });

            mp.events.add('ui:showCharacterSelection', () => {
                setAppState(prev => ({ 
                    ...prev, 
                    currentScreen: 'character-selection', 
                    isAuthenticated: true 
                }));
            });

            mp.events.add('ui:enterGame', () => {
                setAppState(prev => ({ 
                    ...prev, 
                    currentScreen: 'game', 
                    hasCharacter: true 
                }));
            });

            mp.events.add('ui:toggleDebug', () => {
                setAppState(prev => ({ ...prev, showDebug: !prev.showDebug }));
            });
        }

        return () => {
            // Cleanup event listeners
            if (typeof mp !== 'undefined') {
                mp.events.remove('ui:showLogin');
                mp.events.remove('ui:showCharacterSelection');
                mp.events.remove('ui:enterGame');
                mp.events.remove('ui:toggleDebug');
            }
        };
    }, []);

    const initializeApp = async (): Promise<void> => {
        try {
            // Simulate initialization delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Check if player needs authentication
            setAppState(prev => ({ 
                ...prev, 
                isLoading: false, 
                currentScreen: 'auth' 
            }));
        } catch (error) {
            console.error('Failed to initialize app:', error);
        }
    };

    const renderCurrentScreen = (): React.ReactNode => {
        switch (appState.currentScreen) {
            case 'loading':
                return <LoadingScreen />;
            
            case 'auth':
                return (
                    <AuthenticationModal
                        isOpen={true}
                        onAuthenticated={() => {
                            setAppState(prev => ({ 
                                ...prev, 
                                isAuthenticated: true, 
                                currentScreen: 'character-selection' 
                            }));
                        }}
                    />
                );
            
            case 'character-selection':
                return (
                    <CharacterSelection
                        onCharacterSelected={() => {
                            setAppState(prev => ({ 
                                ...prev, 
                                hasCharacter: true, 
                                currentScreen: 'game' 
                            }));
                        }}
                    />
                );
            
            case 'game':
                return <HUD />;
            
            default:
                return <LoadingScreen />;
        }
    };

    return (
        <ThemeProvider>
            <UIProvider>
                <div className="app-container">
                    {/* Main UI Content */}
                    {renderCurrentScreen()}
                    
                    {/* Always visible components */}
                    <NotificationSystem />
                    
                    {/* Debug panel (only in development or when enabled) */}
                    {appState.showDebug && <DebugPanel />}
                    
                    {/* Global overlays */}
                    <div id="modal-root" />
                    <div id="tooltip-root" />
                </div>
            </UIProvider>
        </ThemeProvider>
    );
};

export default App;
