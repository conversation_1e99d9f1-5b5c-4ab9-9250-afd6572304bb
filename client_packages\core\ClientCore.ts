/**
 * Client Core - Main client management class
 * Handles initialization, module loading, and client lifecycle
 */

import { Logger } from '../utils/Logger';
import { EventManager } from '../events/EventManager';
import { UIManager } from '../ui/UIManager';
import { InputManager } from '../input/InputManager';
import { CameraManager } from '../camera/CameraManager';
import { AudioManager } from '../audio/AudioManager';
import { NetworkManager } from '../network/NetworkManager';
import { ConfigManager } from '../config/ConfigManager';

export class ClientCore {
    private static instance: ClientCore;
    private isInitialized: boolean = false;
    private isRunning: boolean = false;

    // Core managers
    private eventManager: EventManager;
    private uiManager: UIManager;
    private inputManager: InputManager;
    private cameraManager: CameraManager;
    private audioManager: AudioManager;
    private networkManager: NetworkManager;
    private configManager: ConfigManager;

    constructor() {
        if (ClientCore.instance) {
            return ClientCore.instance;
        }
        ClientCore.instance = this;
    }

    /**
     * Initialize all client components
     */
    public async start(): Promise<void> {
        try {
            Logger.info('🔧 Initializing client core...');

            // Initialize configuration
            this.configManager = new ConfigManager();
            await this.configManager.load();

            // Initialize event system
            this.eventManager = new EventManager();

            // Initialize network manager
            this.networkManager = new NetworkManager(this.eventManager);

            // Initialize managers
            this.uiManager = new UIManager(this.eventManager);
            this.inputManager = new InputManager(this.eventManager);
            this.cameraManager = new CameraManager(this.eventManager);
            this.audioManager = new AudioManager(this.eventManager);

            // Register all events
            this.registerEvents();

            // Initialize all managers
            await this.initializeManagers();

            this.isInitialized = true;
            this.isRunning = true;

            Logger.success('✅ Client core initialized successfully');
        } catch (error) {
            Logger.error('❌ Failed to initialize client core:', error);
            throw error;
        }
    }

    /**
     * Shutdown client gracefully
     */
    public async shutdown(): Promise<void> {
        if (!this.isRunning) return;

        Logger.info('🛑 Shutting down client core...');

        try {
            // Shutdown all managers
            await this.shutdownManagers();

            this.isRunning = false;
            Logger.success('✅ Client shutdown completed');
        } catch (error) {
            Logger.error('❌ Error during shutdown:', error);
        }
    }

    /**
     * Initialize all managers
     */
    private async initializeManagers(): Promise<void> {
        try {
            await this.networkManager.initialize();
            await this.uiManager.initialize();
            await this.inputManager.initialize();
            await this.cameraManager.initialize();
            await this.audioManager.initialize();

            Logger.success('✅ All managers initialized');
        } catch (error) {
            Logger.error('❌ Failed to initialize managers:', error);
            throw error;
        }
    }

    /**
     * Shutdown all managers
     */
    private async shutdownManagers(): Promise<void> {
        try {
            await this.audioManager.shutdown();
            await this.cameraManager.shutdown();
            await this.inputManager.shutdown();
            await this.uiManager.shutdown();
            await this.networkManager.shutdown();

            Logger.success('✅ All managers shut down');
        } catch (error) {
            Logger.error('❌ Error shutting down managers:', error);
        }
    }

    /**
     * Register all client events
     */
    private registerEvents(): void {
        // RAGE MP Events
        mp.events.add('render', this.onRender.bind(this));
        mp.events.add('browserCreated', this.onBrowserCreated.bind(this));
        mp.events.add('browserLoadingFailed', this.onBrowserLoadingFailed.bind(this));

        // Custom client events
        mp.events.add('client:notification', this.onNotification.bind(this));
        mp.events.add('client:showLoginForm', this.onShowLoginForm.bind(this));
        mp.events.add('client:showRegisterForm', this.onShowRegisterForm.bind(this));
        mp.events.add('client:authError', this.onAuthError.bind(this));
        mp.events.add('client:showCharacterSelection', this.onShowCharacterSelection.bind(this));
        mp.events.add('client:characterLoaded', this.onCharacterLoaded.bind(this));
        mp.events.add('client:characterError', this.onCharacterError.bind(this));
        mp.events.add('client:applySkinData', this.onApplySkinData.bind(this));
        mp.events.add('client:applyClothes', this.onApplyClothes.bind(this));

        Logger.debug('Client events registered');
    }

    /**
     * Handle render event
     */
    private onRender(): void {
        // This is called every frame
        // Update managers that need per-frame updates
        if (this.isRunning) {
            this.uiManager.update();
            this.cameraManager.update();
        }
    }

    /**
     * Handle browser created
     */
    private onBrowserCreated(browser: BrowserMp): void {
        Logger.debug('Browser created:', browser.id);
        this.eventManager.emit('browser:created', browser);
    }

    /**
     * Handle browser loading failed
     */
    private onBrowserLoadingFailed(browser: BrowserMp, url: string, errorCode: number, errorDesc: string): void {
        Logger.error(`Browser loading failed: ${url} (${errorCode}: ${errorDesc})`);
        this.eventManager.emit('browser:loadingFailed', browser, url, errorCode, errorDesc);
    }

    /**
     * Handle notification
     */
    private onNotification(message: string, type: string = 'info'): void {
        this.uiManager.showNotification(message, type);
    }

    /**
     * Handle show login form
     */
    private onShowLoginForm(username?: string): void {
        this.uiManager.showLoginForm(username);
    }

    /**
     * Handle show register form
     */
    private onShowRegisterForm(): void {
        this.uiManager.showRegisterForm();
    }

    /**
     * Handle authentication error
     */
    private onAuthError(message: string): void {
        this.uiManager.showAuthError(message);
    }

    /**
     * Handle show character selection
     */
    private onShowCharacterSelection(characters: any[]): void {
        this.uiManager.showCharacterSelection(characters);
    }

    /**
     * Handle character loaded
     */
    private onCharacterLoaded(characterData: any): void {
        this.uiManager.onCharacterLoaded(characterData);
        this.eventManager.emit('character:loaded', characterData);
    }

    /**
     * Handle character error
     */
    private onCharacterError(message: string): void {
        this.uiManager.showCharacterError(message);
    }

    /**
     * Handle apply skin data
     */
    private onApplySkinData(skinData: any): void {
        // Apply skin customization to local player
        // This would involve RAGE MP native calls
        Logger.debug('Applying skin data:', skinData);
    }

    /**
     * Handle apply clothes
     */
    private onApplyClothes(clothesData: any): void {
        // Apply clothes to local player
        // This would involve RAGE MP native calls
        Logger.debug('Applying clothes:', clothesData);
    }

    // Getters for accessing managers
    public static getInstance(): ClientCore {
        return ClientCore.instance;
    }

    public getEventManager(): EventManager {
        return this.eventManager;
    }

    public getUIManager(): UIManager {
        return this.uiManager;
    }

    public getInputManager(): InputManager {
        return this.inputManager;
    }

    public getCameraManager(): CameraManager {
        return this.cameraManager;
    }

    public getAudioManager(): AudioManager {
        return this.audioManager;
    }

    public getNetworkManager(): NetworkManager {
        return this.networkManager;
    }

    public getConfigManager(): ConfigManager {
        return this.configManager;
    }

    /**
     * Check if client is running
     */
    public isClientRunning(): boolean {
        return this.isRunning;
    }

    /**
     * Check if client is initialized
     */
    public isClientInitialized(): boolean {
        return this.isInitialized;
    }
}
