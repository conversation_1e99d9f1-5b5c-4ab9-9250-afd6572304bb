/**
 * UI Manager
 * Manages all UI interactions and state
 */

import { Logger } from '../utils/Logger';
import { EventManager } from '../events/EventManager';

export interface NotificationData {
    id: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    duration?: number;
    timestamp: Date;
}

export interface UIState {
    isVisible: boolean;
    currentScreen: string;
    notifications: NotificationData[];
    modals: string[];
    hudVisible: boolean;
    chatVisible: boolean;
    cursorVisible: boolean;
}

export class UIManager {
    private eventManager: EventManager;
    private uiState: UIState;
    private notificationCounter: number = 0;

    constructor(eventManager: EventManager) {
        this.eventManager = eventManager;
        this.uiState = {
            isVisible: true,
            currentScreen: 'loading',
            notifications: [],
            modals: [],
            hudVisible: true,
            chatVisible: true,
            cursorVisible: false
        };
    }

    /**
     * Initialize UI Manager
     */
    public async initialize(): Promise<void> {
        try {
            Logger.info('🎨 Initializing UI Manager...');

            // Register UI events
            this.registerEvents();

            // Initialize cursor state
            this.setCursorVisible(false);

            Logger.success('✅ UI Manager initialized');
        } catch (error) {
            Logger.error('❌ Failed to initialize UI Manager:', error);
            throw error;
        }
    }

    /**
     * Shutdown UI Manager
     */
    public async shutdown(): Promise<void> {
        try {
            Logger.info('🛑 Shutting down UI Manager...');
            
            // Clear all notifications
            this.clearAllNotifications();
            
            // Hide cursor
            this.setCursorVisible(false);
            
            Logger.success('✅ UI Manager shut down');
        } catch (error) {
            Logger.error('❌ Error shutting down UI Manager:', error);
        }
    }

    /**
     * Update UI (called every frame)
     */
    public update(): void {
        // Clean up expired notifications
        this.cleanupNotifications();
    }

    /**
     * Register UI events
     */
    private registerEvents(): void {
        // Internal UI events
        this.eventManager.on('ui:showNotification', this.onShowNotification.bind(this));
        this.eventManager.on('ui:hideNotification', this.onHideNotification.bind(this));
        this.eventManager.on('ui:toggleHUD', this.onToggleHUD.bind(this));
        this.eventManager.on('ui:toggleChat', this.onToggleChat.bind(this));
        this.eventManager.on('ui:setCursor', this.onSetCursor.bind(this));

        Logger.debug('UI events registered');
    }

    /**
     * Show notification
     */
    public showNotification(message: string, type: NotificationData['type'] = 'info', duration: number = 5000): void {
        const notification: NotificationData = {
            id: `notification_${++this.notificationCounter}`,
            message,
            type,
            duration,
            timestamp: new Date()
        };

        this.uiState.notifications.push(notification);

        // Emit to React components
        this.emitUIEvent('notification:add', notification);

        Logger.debug(`Notification shown: ${message} (${type})`);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification(notification.id);
            }, duration);
        }
    }

    /**
     * Hide notification
     */
    public hideNotification(notificationId: string): void {
        const index = this.uiState.notifications.findIndex(n => n.id === notificationId);
        if (index !== -1) {
            const notification = this.uiState.notifications.splice(index, 1)[0];
            this.emitUIEvent('notification:remove', notification);
            Logger.debug(`Notification hidden: ${notificationId}`);
        }
    }

    /**
     * Clear all notifications
     */
    public clearAllNotifications(): void {
        this.uiState.notifications = [];
        this.emitUIEvent('notification:clear');
        Logger.debug('All notifications cleared');
    }

    /**
     * Show login form
     */
    public showLoginForm(username?: string): void {
        this.uiState.currentScreen = 'login';
        this.setCursorVisible(true);
        this.emitUIEvent('auth:showLogin', { username });
        Logger.debug('Login form shown');
    }

    /**
     * Show register form
     */
    public showRegisterForm(): void {
        this.uiState.currentScreen = 'register';
        this.setCursorVisible(true);
        this.emitUIEvent('auth:showRegister');
        Logger.debug('Register form shown');
    }

    /**
     * Show authentication error
     */
    public showAuthError(message: string): void {
        this.showNotification(message, 'error', 8000);
        this.emitUIEvent('auth:error', { message });
    }

    /**
     * Show character selection
     */
    public showCharacterSelection(characters: any[]): void {
        this.uiState.currentScreen = 'character-selection';
        this.setCursorVisible(true);
        this.emitUIEvent('character:showSelection', { characters });
        Logger.debug('Character selection shown');
    }

    /**
     * Show character error
     */
    public showCharacterError(message: string): void {
        this.showNotification(message, 'error', 8000);
        this.emitUIEvent('character:error', { message });
    }

    /**
     * Handle character loaded
     */
    public onCharacterLoaded(characterData: any): void {
        this.uiState.currentScreen = 'game';
        this.setCursorVisible(false);
        this.emitUIEvent('character:loaded', { characterData });
        this.showNotification(`Welcome back, ${characterData.name}!`, 'success');
        Logger.debug('Character loaded, entering game');
    }

    /**
     * Toggle HUD visibility
     */
    public toggleHUD(): void {
        this.uiState.hudVisible = !this.uiState.hudVisible;
        this.emitUIEvent('hud:toggle', { visible: this.uiState.hudVisible });
        Logger.debug(`HUD ${this.uiState.hudVisible ? 'shown' : 'hidden'}`);
    }

    /**
     * Set HUD visibility
     */
    public setHUDVisible(visible: boolean): void {
        this.uiState.hudVisible = visible;
        this.emitUIEvent('hud:setVisible', { visible });
        Logger.debug(`HUD visibility set to: ${visible}`);
    }

    /**
     * Toggle chat visibility
     */
    public toggleChat(): void {
        this.uiState.chatVisible = !this.uiState.chatVisible;
        this.emitUIEvent('chat:toggle', { visible: this.uiState.chatVisible });
        Logger.debug(`Chat ${this.uiState.chatVisible ? 'shown' : 'hidden'}`);
    }

    /**
     * Set cursor visibility
     */
    public setCursorVisible(visible: boolean): void {
        this.uiState.cursorVisible = visible;
        
        if (typeof mp !== 'undefined' && mp.gui && mp.gui.cursor) {
            mp.gui.cursor.show(visible, visible);
        }
        
        this.emitUIEvent('cursor:setVisible', { visible });
        Logger.debug(`Cursor visibility set to: ${visible}`);
    }

    /**
     * Open modal
     */
    public openModal(modalName: string, data?: any): void {
        if (!this.uiState.modals.includes(modalName)) {
            this.uiState.modals.push(modalName);
            this.emitUIEvent('modal:open', { modalName, data });
            Logger.debug(`Modal opened: ${modalName}`);
        }
    }

    /**
     * Close modal
     */
    public closeModal(modalName: string): void {
        const index = this.uiState.modals.indexOf(modalName);
        if (index !== -1) {
            this.uiState.modals.splice(index, 1);
            this.emitUIEvent('modal:close', { modalName });
            Logger.debug(`Modal closed: ${modalName}`);
        }
    }

    /**
     * Close all modals
     */
    public closeAllModals(): void {
        this.uiState.modals = [];
        this.emitUIEvent('modal:closeAll');
        Logger.debug('All modals closed');
    }

    /**
     * Event handlers
     */
    private onShowNotification(data: { message: string; type: string; duration?: number }): void {
        this.showNotification(data.message, data.type as NotificationData['type'], data.duration);
    }

    private onHideNotification(data: { id: string }): void {
        this.hideNotification(data.id);
    }

    private onToggleHUD(): void {
        this.toggleHUD();
    }

    private onToggleChat(): void {
        this.toggleChat();
    }

    private onSetCursor(data: { visible: boolean }): void {
        this.setCursorVisible(data.visible);
    }

    /**
     * Emit UI event to React components
     */
    private emitUIEvent(eventName: string, data?: any): void {
        // Emit to internal event system
        this.eventManager.emit(`ui:${eventName}`, data);

        // Also emit to window for React components to listen
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent(`ui:${eventName}`, { detail: data }));
        }
    }

    /**
     * Clean up expired notifications
     */
    private cleanupNotifications(): void {
        const now = Date.now();
        this.uiState.notifications = this.uiState.notifications.filter(notification => {
            if (notification.duration && notification.duration > 0) {
                const elapsed = now - notification.timestamp.getTime();
                return elapsed < notification.duration;
            }
            return true;
        });
    }

    /**
     * Get current UI state
     */
    public getUIState(): UIState {
        return { ...this.uiState };
    }

    /**
     * Get notifications
     */
    public getNotifications(): NotificationData[] {
        return [...this.uiState.notifications];
    }

    /**
     * Check if modal is open
     */
    public isModalOpen(modalName: string): boolean {
        return this.uiState.modals.includes(modalName);
    }

    /**
     * Get open modals
     */
    public getOpenModals(): string[] {
        return [...this.uiState.modals];
    }
}
