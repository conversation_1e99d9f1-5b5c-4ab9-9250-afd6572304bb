"use strict";
/**
 * Configuration Manager
 * Handles all server configuration settings
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const Logger_1 = require("../utils/Logger");
class ConfigManager {
    constructor() {
        this.configPath = './server-config.json';
        this.setDefaultConfig();
    }
    /**
     * Load configuration from file
     */
    async load() {
        try {
            // In a real implementation, you would read from a file
            // For now, we'll use the default configuration
            Logger_1.Logger.info('📋 Loading server configuration...');
            // TODO: Implement file reading
            // const configData = await fs.readFile(this.configPath, 'utf8');
            // this.config = { ...this.config, ...JSON.parse(configData) };
            Logger_1.Logger.success('✅ Configuration loaded successfully');
        }
        catch (error) {
            Logger_1.Logger.warn('⚠️ Could not load config file, using defaults');
            Logger_1.Logger.debug('Config load error:', error);
        }
    }
    /**
     * Save configuration to file
     */
    async save() {
        try {
            Logger_1.Logger.info('💾 Saving server configuration...');
            // TODO: Implement file writing
            // await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2));
            Logger_1.Logger.success('✅ Configuration saved successfully');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Failed to save configuration:', error);
            throw error;
        }
    }
    /**
     * Set default configuration values
     */
    setDefaultConfig() {
        this.config = {
            server: {
                name: 'Advanced Roleplay Server',
                maxPlayers: 100,
                port: 22005,
                gamemode: 'roleplay',
                streamDistance: 300.0,
                announce: false
            },
            database: {
                host: 'localhost',
                port: 3306,
                username: 'root',
                password: '',
                database: 'ragemp_roleplay',
                connectionLimit: 10
            },
            security: {
                bcryptRounds: 12,
                sessionTimeout: 3600000, // 1 hour
                maxLoginAttempts: 5,
                lockoutDuration: 900000 // 15 minutes
            },
            gameplay: {
                startingMoney: 5000,
                startingPosition: {
                    x: -1037.8,
                    y: -2738.5,
                    z: 20.2
                },
                respawnTime: 10000, // 10 seconds
                maxCharacters: 3
            },
            ui: {
                theme: 'dark',
                language: 'fa', // Persian
                animations: true
            }
        };
    }
    /**
     * Get configuration value by path
     */
    get(path) {
        const keys = path.split('.');
        let value = this.config;
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            }
            else {
                Logger_1.Logger.warn(`Configuration path not found: ${path}`);
                return undefined;
            }
        }
        return value;
    }
    /**
     * Set configuration value by path
     */
    set(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let target = this.config;
        for (const key of keys) {
            if (!(key in target)) {
                target[key] = {};
            }
            target = target[key];
        }
        if (lastKey) {
            target[lastKey] = value;
        }
    }
    /**
     * Get full configuration
     */
    getAll() {
        return { ...this.config };
    }
    /**
     * Update configuration with partial data
     */
    update(partialConfig) {
        this.config = { ...this.config, ...partialConfig };
    }
    /**
     * Reset to default configuration
     */
    reset() {
        this.setDefaultConfig();
        Logger_1.Logger.info('🔄 Configuration reset to defaults');
    }
    /**
     * Validate configuration
     */
    validate() {
        try {
            // Basic validation
            if (!this.config.server.name || this.config.server.name.length === 0) {
                throw new Error('Server name cannot be empty');
            }
            if (this.config.server.maxPlayers <= 0 || this.config.server.maxPlayers > 1000) {
                throw new Error('Max players must be between 1 and 1000');
            }
            if (this.config.server.port <= 0 || this.config.server.port > 65535) {
                throw new Error('Port must be between 1 and 65535');
            }
            Logger_1.Logger.success('✅ Configuration validation passed');
            return true;
        }
        catch (error) {
            Logger_1.Logger.error('❌ Configuration validation failed:', error);
            return false;
        }
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map