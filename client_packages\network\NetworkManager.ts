/**
 * Network Manager
 * Handles client-server communication
 */

import { Logger } from '../utils/Logger';
import { EventManager } from '../events/EventManager';

export interface NetworkMessage {
    id: string;
    type: string;
    data: any;
    timestamp: Date;
}

export class NetworkManager {
    private eventManager: EventManager;
    private messageQueue: NetworkMessage[] = [];
    private isConnected: boolean = false;
    private messageCounter: number = 0;

    constructor(eventManager: EventManager) {
        this.eventManager = eventManager;
    }

    /**
     * Initialize Network Manager
     */
    public async initialize(): Promise<void> {
        try {
            Logger.info('🌐 Initializing Network Manager...');

            // Register network events
            this.registerEvents();

            // Check connection status
            this.checkConnection();

            Logger.success('✅ Network Manager initialized');
        } catch (error) {
            Logger.error('❌ Failed to initialize Network Manager:', error);
            throw error;
        }
    }

    /**
     * Shutdown Network Manager
     */
    public async shutdown(): Promise<void> {
        try {
            Logger.info('🛑 Shutting down Network Manager...');
            
            // Clear message queue
            this.messageQueue = [];
            
            Logger.success('✅ Network Manager shut down');
        } catch (error) {
            Logger.error('❌ Error shutting down Network Manager:', error);
        }
    }

    /**
     * Register network events
     */
    private registerEvents(): void {
        // Register RAGE MP events
        if (typeof mp !== 'undefined') {
            mp.events.add('network:message', this.onNetworkMessage.bind(this));
            mp.events.add('network:connected', this.onConnected.bind(this));
            mp.events.add('network:disconnected', this.onDisconnected.bind(this));
        }

        // Register internal events
        this.eventManager.on('network:send', this.onSendMessage.bind(this));

        Logger.debug('Network events registered');
    }

    /**
     * Send message to server
     */
    public sendToServer(eventName: string, data?: any): void {
        try {
            if (typeof mp !== 'undefined') {
                mp.events.callRemote(eventName, data);
                
                Logger.debug(`Message sent to server: ${eventName}`, data);
                
                // Add to message queue for tracking
                this.addToMessageQueue('outgoing', eventName, data);
            } else {
                Logger.warn('Cannot send message: RAGE MP not available');
            }
        } catch (error) {
            Logger.error(`Failed to send message to server: ${eventName}`, error);
        }
    }

    /**
     * Send unreliable message to server
     */
    public sendToServerUnreliable(eventName: string, data?: any): void {
        try {
            if (typeof mp !== 'undefined') {
                mp.events.callRemoteUnreliable(eventName, data);
                
                Logger.debug(`Unreliable message sent to server: ${eventName}`, data);
                
                // Add to message queue for tracking
                this.addToMessageQueue('outgoing-unreliable', eventName, data);
            } else {
                Logger.warn('Cannot send unreliable message: RAGE MP not available');
            }
        } catch (error) {
            Logger.error(`Failed to send unreliable message to server: ${eventName}`, error);
        }
    }

    /**
     * Handle incoming network message
     */
    private onNetworkMessage(eventName: string, data: any): void {
        try {
            Logger.debug(`Message received from server: ${eventName}`, data);
            
            // Add to message queue for tracking
            this.addToMessageQueue('incoming', eventName, data);
            
            // Emit to internal event system
            this.eventManager.emit(`server:${eventName}`, data);
            
        } catch (error) {
            Logger.error(`Error handling network message: ${eventName}`, error);
        }
    }

    /**
     * Handle connection established
     */
    private onConnected(): void {
        this.isConnected = true;
        Logger.success('🌐 Connected to server');
        this.eventManager.emit('network:connected');
    }

    /**
     * Handle connection lost
     */
    private onDisconnected(): void {
        this.isConnected = false;
        Logger.warn('🌐 Disconnected from server');
        this.eventManager.emit('network:disconnected');
    }

    /**
     * Handle send message event
     */
    private onSendMessage(data: { eventName: string; payload?: any; unreliable?: boolean }): void {
        if (data.unreliable) {
            this.sendToServerUnreliable(data.eventName, data.payload);
        } else {
            this.sendToServer(data.eventName, data.payload);
        }
    }

    /**
     * Add message to tracking queue
     */
    private addToMessageQueue(type: string, eventName: string, data: any): void {
        const message: NetworkMessage = {
            id: `msg_${++this.messageCounter}`,
            type: `${type}:${eventName}`,
            data,
            timestamp: new Date()
        };

        this.messageQueue.push(message);

        // Keep only last 100 messages
        if (this.messageQueue.length > 100) {
            this.messageQueue.shift();
        }
    }

    /**
     * Check connection status
     */
    private checkConnection(): void {
        // In RAGE MP, we assume we're connected if mp object exists
        this.isConnected = typeof mp !== 'undefined';
        Logger.debug(`Connection status: ${this.isConnected ? 'Connected' : 'Disconnected'}`);
    }

    /**
     * Get connection status
     */
    public isNetworkConnected(): boolean {
        return this.isConnected;
    }

    /**
     * Get message history
     */
    public getMessageHistory(): NetworkMessage[] {
        return [...this.messageQueue];
    }

    /**
     * Clear message history
     */
    public clearMessageHistory(): void {
        this.messageQueue = [];
        Logger.debug('Message history cleared');
    }

    /**
     * Get network statistics
     */
    public getNetworkStats(): { [key: string]: number } {
        const stats: { [key: string]: number } = {
            totalMessages: this.messageQueue.length,
            incoming: 0,
            outgoing: 0,
            outgoingUnreliable: 0
        };

        this.messageQueue.forEach(message => {
            if (message.type.startsWith('incoming:')) {
                stats.incoming++;
            } else if (message.type.startsWith('outgoing-unreliable:')) {
                stats.outgoingUnreliable++;
            } else if (message.type.startsWith('outgoing:')) {
                stats.outgoing++;
            }
        });

        return stats;
    }
}
