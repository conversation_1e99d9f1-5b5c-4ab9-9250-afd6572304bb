"use strict";
/**
 * Database Manager
 * Handles all database connections and operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const Logger_1 = require("../utils/Logger");
const ConfigManager_1 = require("../config/ConfigManager");
class DatabaseManager {
    constructor() {
        this.pool = null;
        this.isConnected = false;
        this.configManager = new ConfigManager_1.ConfigManager();
    }
    /**
     * Connect to the database
     */
    async connect() {
        try {
            Logger_1.Logger.info('🔌 Connecting to database...');
            const dbConfig = this.configManager.get('database');
            // TODO: Implement actual MySQL2 connection
            // For now, we'll simulate a connection
            this.pool = this.createMockPool();
            // Test connection
            await this.testConnection();
            this.isConnected = true;
            Logger_1.Logger.success('✅ Database connected successfully');
            // Initialize database schema
            await this.initializeSchema();
        }
        catch (error) {
            Logger_1.Logger.error('❌ Failed to connect to database:', error);
            throw error;
        }
    }
    /**
     * Disconnect from the database
     */
    async disconnect() {
        if (!this.pool || !this.isConnected)
            return;
        try {
            Logger_1.Logger.info('🔌 Disconnecting from database...');
            await this.pool.end();
            this.pool = null;
            this.isConnected = false;
            Logger_1.Logger.success('✅ Database disconnected successfully');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Error disconnecting from database:', error);
        }
    }
    /**
     * Test database connection
     */
    async testConnection() {
        if (!this.pool)
            throw new Error('Database pool not initialized');
        const result = await this.pool.execute('SELECT 1 as test');
        if (!result || result.length === 0) {
            throw new Error('Database connection test failed');
        }
    }
    /**
     * Initialize database schema
     */
    async initializeSchema() {
        Logger_1.Logger.info('📋 Initializing database schema...');
        const tables = [
            this.createUsersTable(),
            this.createCharactersTable(),
            this.createVehiclesTable(),
            this.createPropertiesTable(),
            this.createBusinessesTable(),
            this.createInventoryTable(),
            this.createBankAccountsTable(),
            this.createLogTable()
        ];
        for (const tableQuery of tables) {
            try {
                await this.execute(tableQuery);
            }
            catch (error) {
                Logger_1.Logger.warn(`⚠️ Table creation warning: ${error}`);
            }
        }
        Logger_1.Logger.success('✅ Database schema initialized');
    }
    /**
     * Execute a database query
     */
    async execute(query, params = []) {
        if (!this.pool || !this.isConnected) {
            throw new Error('Database not connected');
        }
        try {
            Logger_1.Logger.debug('🔍 Executing query:', { query, params });
            const result = await this.pool.execute(query, params);
            return result;
        }
        catch (error) {
            Logger_1.Logger.error('❌ Database query error:', { query, params, error });
            throw error;
        }
    }
    /**
     * Get a database connection from the pool
     */
    async getConnection() {
        if (!this.pool || !this.isConnected) {
            throw new Error('Database not connected');
        }
        return await this.pool.getConnection();
    }
    /**
     * Create users table
     */
    createUsersTable() {
        return `
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                social_club VARCHAR(100) UNIQUE NOT NULL,
                serial VARCHAR(100) UNIQUE NOT NULL,
                ip_address VARCHAR(45),
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                login_attempts INT DEFAULT 0,
                locked_until TIMESTAMP NULL,
                is_banned BOOLEAN DEFAULT FALSE,
                ban_reason TEXT NULL,
                admin_level INT DEFAULT 0,
                total_playtime INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `;
    }
    /**
     * Create characters table
     */
    createCharactersTable() {
        return `
            CREATE TABLE IF NOT EXISTS characters (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                name VARCHAR(50) NOT NULL,
                age INT NOT NULL,
                gender ENUM('male', 'female') NOT NULL,
                money INT DEFAULT 5000,
                bank_money INT DEFAULT 0,
                position_x FLOAT DEFAULT -1037.8,
                position_y FLOAT DEFAULT -2738.5,
                position_z FLOAT DEFAULT 20.2,
                heading FLOAT DEFAULT 0.0,
                dimension INT DEFAULT 0,
                interior INT DEFAULT 0,
                health INT DEFAULT 100,
                armor INT DEFAULT 0,
                hunger INT DEFAULT 100,
                thirst INT DEFAULT 100,
                stress INT DEFAULT 0,
                job VARCHAR(50) DEFAULT 'unemployed',
                job_rank INT DEFAULT 0,
                job_salary INT DEFAULT 0,
                faction VARCHAR(50) DEFAULT NULL,
                faction_rank INT DEFAULT 0,
                skin_data JSON,
                clothes_data JSON,
                is_active BOOLEAN DEFAULT TRUE,
                playtime INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_name (name)
            )
        `;
    }
    /**
     * Create vehicles table
     */
    createVehiclesTable() {
        return `
            CREATE TABLE IF NOT EXISTS vehicles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                owner_id INT NOT NULL,
                model VARCHAR(50) NOT NULL,
                position_x FLOAT NOT NULL,
                position_y FLOAT NOT NULL,
                position_z FLOAT NOT NULL,
                heading FLOAT DEFAULT 0.0,
                dimension INT DEFAULT 0,
                color1 JSON,
                color2 JSON,
                number_plate VARCHAR(8) UNIQUE NOT NULL,
                engine_health FLOAT DEFAULT 1000.0,
                body_health FLOAT DEFAULT 1000.0,
                fuel FLOAT DEFAULT 100.0,
                mileage FLOAT DEFAULT 0.0,
                locked BOOLEAN DEFAULT TRUE,
                engine_on BOOLEAN DEFAULT FALSE,
                modifications JSON,
                insurance_expires TIMESTAMP NULL,
                registration_expires TIMESTAMP NULL,
                is_impounded BOOLEAN DEFAULT FALSE,
                impound_reason TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (owner_id) REFERENCES characters(id) ON DELETE CASCADE,
                INDEX idx_owner_id (owner_id),
                INDEX idx_number_plate (number_plate)
            )
        `;
    }
    /**
     * Create properties table
     */
    createPropertiesTable() {
        return `
            CREATE TABLE IF NOT EXISTS properties (
                id INT AUTO_INCREMENT PRIMARY KEY,
                owner_id INT NULL,
                name VARCHAR(100) NOT NULL,
                type ENUM('house', 'apartment', 'garage', 'warehouse') NOT NULL,
                price INT NOT NULL,
                position_x FLOAT NOT NULL,
                position_y FLOAT NOT NULL,
                position_z FLOAT NOT NULL,
                interior_id INT DEFAULT 0,
                locked BOOLEAN DEFAULT TRUE,
                for_sale BOOLEAN DEFAULT FALSE,
                rent_price INT DEFAULT 0,
                furniture_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (owner_id) REFERENCES characters(id) ON DELETE SET NULL,
                INDEX idx_owner_id (owner_id),
                INDEX idx_type (type)
            )
        `;
    }
    /**
     * Create businesses table
     */
    createBusinessesTable() {
        return `
            CREATE TABLE IF NOT EXISTS businesses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                owner_id INT NULL,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(50) NOT NULL,
                position_x FLOAT NOT NULL,
                position_y FLOAT NOT NULL,
                position_z FLOAT NOT NULL,
                price INT NOT NULL,
                revenue INT DEFAULT 0,
                stock JSON,
                employees JSON,
                is_open BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (owner_id) REFERENCES characters(id) ON DELETE SET NULL,
                INDEX idx_owner_id (owner_id),
                INDEX idx_type (type)
            )
        `;
    }
    /**
     * Create inventory table
     */
    createInventoryTable() {
        return `
            CREATE TABLE IF NOT EXISTS inventory (
                id INT AUTO_INCREMENT PRIMARY KEY,
                owner_type ENUM('character', 'vehicle', 'property') NOT NULL,
                owner_id INT NOT NULL,
                item_name VARCHAR(50) NOT NULL,
                quantity INT NOT NULL,
                metadata JSON,
                slot INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_owner (owner_type, owner_id),
                INDEX idx_item_name (item_name)
            )
        `;
    }
    /**
     * Create bank accounts table
     */
    createBankAccountsTable() {
        return `
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                character_id INT NOT NULL,
                account_number VARCHAR(20) UNIQUE NOT NULL,
                balance INT DEFAULT 0,
                account_type ENUM('checking', 'savings', 'business') DEFAULT 'checking',
                pin VARCHAR(4) NOT NULL,
                is_frozen BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,
                INDEX idx_character_id (character_id),
                INDEX idx_account_number (account_number)
            )
        `;
    }
    /**
     * Create log table
     */
    createLogTable() {
        return `
            CREATE TABLE IF NOT EXISTS server_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR', 'SUCCESS') NOT NULL,
                message TEXT NOT NULL,
                data JSON,
                source VARCHAR(100),
                player_id INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_level (level),
                INDEX idx_created_at (created_at),
                INDEX idx_player_id (player_id)
            )
        `;
    }
    /**
     * Create a mock pool for development
     * TODO: Replace with actual MySQL2 implementation
     */
    createMockPool() {
        return {
            async getConnection() {
                return {
                    async execute(query, params) {
                        // Mock implementation
                        return [{ test: 1 }];
                    },
                    async end() {
                        // Mock implementation
                    }
                };
            },
            async execute(query, params) {
                // Mock implementation
                return [{ test: 1 }];
            },
            async end() {
                // Mock implementation
            }
        };
    }
    /**
     * Check if database is connected
     */
    isConnectedToDatabase() {
        return this.isConnected;
    }
}
exports.DatabaseManager = DatabaseManager;
//# sourceMappingURL=DatabaseManager.js.map