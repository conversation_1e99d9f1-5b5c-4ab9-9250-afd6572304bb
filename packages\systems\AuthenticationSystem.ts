/**
 * Authentication System
 * Handles user registration, login, and session management
 */

import { Logger } from '../utils/Logger';
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';
import { ExtendedPlayer } from '../managers/PlayerManager';

export interface LoginData {
    username: string;
    password: string;
}

export interface RegisterData {
    username: string;
    email: string;
    password: string;
    confirmPassword: string;
}

export class AuthenticationSystem {
    private databaseManager: DatabaseManager;
    private eventManager: EventManager;
    private maxLoginAttempts: number = 5;
    private lockoutDuration: number = 15 * 60 * 1000; // 15 minutes

    constructor() {
        this.databaseManager = new DatabaseManager();
        this.eventManager = new EventManager();
    }

    /**
     * Set dependencies (called by ServerCore)
     */
    public setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void {
        this.databaseManager = databaseManager;
        this.eventManager = eventManager;
    }

    /**
     * Show login screen to player
     */
    public async showLoginScreen(player: ExtendedPlayer): Promise<void> {
        try {
            Logger.info(`🔐 Showing login screen to ${player.name}`);

            // Check if player already has an account
            const existingUser = await this.checkExistingUser(player);
            
            if (existingUser) {
                // Show login form
                player.call('client:showLoginForm', [existingUser.username]);
            } else {
                // Show registration form
                player.call('client:showRegisterForm');
            }

            // Set player in authentication dimension
            player.dimension = 999;
            
        } catch (error) {
            Logger.error(`Failed to show login screen for ${player.name}:`, error);
        }
    }

    /**
     * Handle player login attempt
     */
    public async handleLogin(player: ExtendedPlayer, data: LoginData): Promise<void> {
        try {
            Logger.info(`🔐 Login attempt from ${player.name}: ${data.username}`);

            // Validate input
            if (!this.validateLoginData(data)) {
                this.sendAuthError(player, 'Invalid login data');
                return;
            }

            // Check if player is locked out
            if (await this.isPlayerLockedOut(player)) {
                this.sendAuthError(player, 'Account temporarily locked due to too many failed attempts');
                return;
            }

            // Get user from database
            const user = await this.getUserByUsername(data.username);
            if (!user) {
                await this.handleFailedLogin(player, 'Invalid username or password');
                return;
            }

            // Verify password
            const isPasswordValid = await this.verifyPassword(data.password, user.password_hash);
            if (!isPasswordValid) {
                await this.handleFailedLogin(player, 'Invalid username or password');
                return;
            }

            // Check if user is banned
            if (user.is_banned) {
                this.sendAuthError(player, `Account banned: ${user.ban_reason || 'No reason specified'}`);
                return;
            }

            // Successful login
            await this.handleSuccessfulLogin(player, user);

        } catch (error) {
            Logger.error(`Login error for ${player.name}:`, error);
            this.sendAuthError(player, 'Login failed due to server error');
        }
    }

    /**
     * Handle player registration
     */
    public async handleRegister(player: ExtendedPlayer, data: RegisterData): Promise<void> {
        try {
            Logger.info(`📝 Registration attempt from ${player.name}: ${data.username}`);

            // Validate input
            const validationError = this.validateRegisterData(data);
            if (validationError) {
                this.sendAuthError(player, validationError);
                return;
            }

            // Check if username already exists
            const existingUser = await this.getUserByUsername(data.username);
            if (existingUser) {
                this.sendAuthError(player, 'Username already exists');
                return;
            }

            // Check if email already exists
            const existingEmail = await this.getUserByEmail(data.email);
            if (existingEmail) {
                this.sendAuthError(player, 'Email already registered');
                return;
            }

            // Check if social club already exists
            const existingSocialClub = await this.getUserBySocialClub(player.socialClub);
            if (existingSocialClub) {
                this.sendAuthError(player, 'This Social Club account is already registered');
                return;
            }

            // Hash password
            const passwordHash = await this.hashPassword(data.password);

            // Create user account
            const userId = await this.createUserAccount(player, data, passwordHash);
            if (!userId) {
                this.sendAuthError(player, 'Failed to create account');
                return;
            }

            // Load user data
            const newUser = await this.getUserById(userId);
            if (!newUser) {
                this.sendAuthError(player, 'Failed to load account data');
                return;
            }

            // Successful registration and login
            await this.handleSuccessfulLogin(player, newUser);

            Logger.success(`✅ New user registered: ${data.username} (${player.name})`);

        } catch (error) {
            Logger.error(`Registration error for ${player.name}:`, error);
            this.sendAuthError(player, 'Registration failed due to server error');
        }
    }

    /**
     * Handle successful login
     */
    private async handleSuccessfulLogin(player: ExtendedPlayer, user: any): Promise<void> {
        try {
            // Update player data
            player.userData = {
                id: user.id,
                username: user.username,
                email: user.email,
                socialClub: user.social_club,
                serial: user.serial,
                ipAddress: user.ip_address,
                registrationDate: user.registration_date,
                lastLogin: user.last_login,
                loginAttempts: 0,
                lockedUntil: null,
                isBanned: user.is_banned,
                banReason: user.ban_reason,
                adminLevel: user.admin_level,
                totalPlaytime: user.total_playtime
            };

            player.isLoggedIn = true;
            player.loginAttempts = 0;

            // Update database
            await this.databaseManager.execute(
                'UPDATE users SET last_login = NOW(), login_attempts = 0, locked_until = NULL, ip_address = ? WHERE id = ?',
                [player.ip, user.id]
            );

            // Move to main dimension
            player.dimension = 0;

            // Show character selection
            await this.showCharacterSelection(player);

            // Emit login event
            await this.eventManager.emit('player:login', player);

            Logger.success(`✅ Player logged in: ${user.username} (${player.name})`);

        } catch (error) {
            Logger.error(`Error in successful login for ${player.name}:`, error);
        }
    }

    /**
     * Handle failed login
     */
    private async handleFailedLogin(player: ExtendedPlayer, message: string): Promise<void> {
        player.loginAttempts++;

        // Update database
        const lockoutTime = player.loginAttempts >= this.maxLoginAttempts 
            ? new Date(Date.now() + this.lockoutDuration) 
            : null;

        if (player.userData?.id) {
            await this.databaseManager.execute(
                'UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?',
                [player.loginAttempts, lockoutTime, player.userData.id]
            );
        }

        // Send error message
        if (player.loginAttempts >= this.maxLoginAttempts) {
            this.sendAuthError(player, 'Too many failed attempts. Account locked for 15 minutes.');
        } else {
            const attemptsLeft = this.maxLoginAttempts - player.loginAttempts;
            this.sendAuthError(player, `${message}. ${attemptsLeft} attempts remaining.`);
        }

        Logger.warn(`Failed login attempt: ${player.name} (${player.loginAttempts}/${this.maxLoginAttempts})`);
    }

    /**
     * Show character selection screen
     */
    private async showCharacterSelection(player: ExtendedPlayer): Promise<void> {
        if (!player.userData) return;

        try {
            // Get player's characters
            const characters = await this.databaseManager.execute(
                'SELECT * FROM characters WHERE user_id = ? AND is_active = TRUE',
                [player.userData.id]
            );

            // Send character data to client
            player.call('client:showCharacterSelection', [characters || []]);

        } catch (error) {
            Logger.error(`Failed to show character selection for ${player.name}:`, error);
        }
    }

    /**
     * Check if player already has an account
     */
    private async checkExistingUser(player: ExtendedPlayer): Promise<any> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT username FROM users WHERE social_club = ? OR serial = ?',
                [player.socialClub, player.serial]
            );

            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.error('Failed to check existing user:', error);
            return null;
        }
    }

    /**
     * Validate login data
     */
    private validateLoginData(data: LoginData): boolean {
        return !!(data.username && data.password && 
                 data.username.length >= 3 && data.username.length <= 50 &&
                 data.password.length >= 6);
    }

    /**
     * Validate registration data
     */
    private validateRegisterData(data: RegisterData): string | null {
        if (!data.username || data.username.length < 3 || data.username.length > 50) {
            return 'Username must be between 3 and 50 characters';
        }

        if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
            return 'Username can only contain letters, numbers, and underscores';
        }

        if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            return 'Invalid email address';
        }

        if (!data.password || data.password.length < 6) {
            return 'Password must be at least 6 characters long';
        }

        if (data.password !== data.confirmPassword) {
            return 'Passwords do not match';
        }

        return null;
    }

    /**
     * Check if player is locked out
     */
    private async isPlayerLockedOut(player: ExtendedPlayer): Promise<boolean> {
        if (!player.userData?.id) return false;

        try {
            const result = await this.databaseManager.execute(
                'SELECT locked_until FROM users WHERE id = ?',
                [player.userData.id]
            );

            if (result && result.length > 0 && result[0].locked_until) {
                return new Date(result[0].locked_until) > new Date();
            }

            return false;
        } catch (error) {
            Logger.error('Failed to check lockout status:', error);
            return false;
        }
    }

    /**
     * Get user by username
     */
    private async getUserByUsername(username: string): Promise<any> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT * FROM users WHERE username = ?',
                [username]
            );

            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.error('Failed to get user by username:', error);
            return null;
        }
    }

    /**
     * Get user by email
     */
    private async getUserByEmail(email: string): Promise<any> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT * FROM users WHERE email = ?',
                [email]
            );

            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.error('Failed to get user by email:', error);
            return null;
        }
    }

    /**
     * Get user by social club
     */
    private async getUserBySocialClub(socialClub: string): Promise<any> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT * FROM users WHERE social_club = ?',
                [socialClub]
            );

            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.error('Failed to get user by social club:', error);
            return null;
        }
    }

    /**
     * Get user by ID
     */
    private async getUserById(id: number): Promise<any> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT * FROM users WHERE id = ?',
                [id]
            );

            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.error('Failed to get user by ID:', error);
            return null;
        }
    }

    /**
     * Hash password
     */
    private async hashPassword(password: string): Promise<string> {
        // TODO: Implement bcrypt hashing
        // For now, return a mock hash
        return `hashed_${password}`;
    }

    /**
     * Verify password
     */
    private async verifyPassword(password: string, hash: string): Promise<boolean> {
        // TODO: Implement bcrypt verification
        // For now, simple comparison
        return hash === `hashed_${password}`;
    }

    /**
     * Create user account
     */
    private async createUserAccount(player: ExtendedPlayer, data: RegisterData, passwordHash: string): Promise<number | null> {
        try {
            const result = await this.databaseManager.execute(
                `INSERT INTO users (username, email, password_hash, social_club, serial, ip_address, registration_date)
                 VALUES (?, ?, ?, ?, ?, ?, NOW())`,
                [data.username, data.email, passwordHash, player.socialClub, player.serial, player.ip]
            );

            return result && result.insertId ? result.insertId : null;
        } catch (error) {
            Logger.error('Failed to create user account:', error);
            return null;
        }
    }

    /**
     * Send authentication error to player
     */
    private sendAuthError(player: ExtendedPlayer, message: string): void {
        player.call('client:authError', [message]);
        Logger.warn(`Auth error for ${player.name}: ${message}`);
    }
}
