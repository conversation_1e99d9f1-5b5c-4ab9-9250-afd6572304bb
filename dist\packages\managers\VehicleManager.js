"use strict";
/**
 * Vehicle Manager
 * Handles all vehicle-related operations and management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VehicleManager = void 0;
const Logger_1 = require("../utils/Logger");
const DatabaseManager_1 = require("../database/DatabaseManager");
const EventManager_1 = require("../events/EventManager");
class VehicleManager {
    constructor() {
        this.vehicles = new Map();
        this.fuelUpdateInterval = null;
        this.databaseManager = new DatabaseManager_1.DatabaseManager();
        this.eventManager = new EventManager_1.EventManager();
        this.startFuelSystem();
    }
    /**
     * Set dependencies (called by ServerCore)
     */
    setDependencies(databaseManager, eventManager) {
        this.databaseManager = databaseManager;
        this.eventManager = eventManager;
    }
    /**
     * Create a new vehicle
     */
    async createVehicle(model, position, heading = 0, ownerId, dimension = 0) {
        try {
            // Create vehicle in game world
            const vehicle = mp.vehicles.new(mp.joaat(model), position, {
                heading,
                dimension,
                numberPlate: this.generateNumberPlate(),
                color: [[255, 255, 255], [255, 255, 255]],
                locked: true,
                engine: false
            });
            if (!vehicle) {
                Logger_1.Logger.error('Failed to create vehicle in game world');
                return null;
            }
            // Extend vehicle object
            const extendedVehicle = this.extendVehicle(vehicle);
            // Create vehicle data
            const vehicleData = {
                model,
                position: { x: position.x, y: position.y, z: position.z },
                heading,
                dimension,
                color1: { r: 255, g: 255, b: 255 },
                color2: { r: 255, g: 255, b: 255 },
                numberPlate: vehicle.numberPlate,
                engineHealth: 1000,
                bodyHealth: 1000,
                fuel: 100,
                mileage: 0,
                locked: true,
                engineOn: false,
                modifications: {},
                isImpounded: false
            };
            if (ownerId) {
                vehicleData.ownerId = ownerId;
            }
            // Save to database if owner is specified
            if (ownerId) {
                const result = await this.databaseManager.execute(`INSERT INTO vehicles (
                        owner_id, model, position_x, position_y, position_z, heading, dimension,
                        color1, color2, number_plate, engine_health, body_health, fuel, mileage,
                        locked, engine_on, modifications, is_impounded
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
                    ownerId, model, position.x, position.y, position.z, heading, dimension,
                    JSON.stringify(vehicleData.color1), JSON.stringify(vehicleData.color2),
                    vehicle.numberPlate, vehicleData.engineHealth, vehicleData.bodyHealth,
                    vehicleData.fuel, vehicleData.mileage, vehicleData.locked, vehicleData.engineOn,
                    JSON.stringify(vehicleData.modifications), vehicleData.isImpounded
                ]);
                if (result && result.insertId) {
                    vehicleData.id = result.insertId;
                }
            }
            extendedVehicle.vehicleData = vehicleData;
            this.vehicles.set(vehicle.id, extendedVehicle);
            Logger_1.Logger.info(`🚗 Vehicle created: ${model} (ID: ${vehicle.id})`);
            await this.eventManager.emit('vehicle:created', extendedVehicle);
            return extendedVehicle;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to create vehicle:', error);
            return null;
        }
    }
    /**
     * Load vehicles from database
     */
    async loadVehiclesFromDatabase() {
        try {
            Logger_1.Logger.info('🚗 Loading vehicles from database...');
            const vehicleData = await this.databaseManager.execute('SELECT * FROM vehicles WHERE is_impounded = FALSE');
            if (!vehicleData || vehicleData.length === 0) {
                Logger_1.Logger.info('No vehicles found in database');
                return;
            }
            for (const data of vehicleData) {
                await this.spawnVehicleFromData(data);
            }
            Logger_1.Logger.success(`✅ Loaded ${vehicleData.length} vehicles from database`);
        }
        catch (error) {
            Logger_1.Logger.error('Failed to load vehicles from database:', error);
        }
    }
    /**
     * Spawn vehicle from database data
     */
    async spawnVehicleFromData(data) {
        try {
            const position = new mp.Vector3(data.position_x, data.position_y, data.position_z);
            const vehicle = mp.vehicles.new(mp.joaat(data.model), position, {
                heading: data.heading,
                dimension: data.dimension,
                numberPlate: data.number_plate,
                color: [JSON.parse(data.color1), JSON.parse(data.color2)],
                locked: data.locked,
                engine: data.engine_on
            });
            if (!vehicle) {
                Logger_1.Logger.error(`Failed to spawn vehicle from database: ${data.model}`);
                return null;
            }
            const extendedVehicle = this.extendVehicle(vehicle);
            extendedVehicle.vehicleData = {
                id: data.id,
                ownerId: data.owner_id,
                model: data.model,
                position: { x: data.position_x, y: data.position_y, z: data.position_z },
                heading: data.heading,
                dimension: data.dimension,
                color1: JSON.parse(data.color1),
                color2: JSON.parse(data.color2),
                numberPlate: data.number_plate,
                engineHealth: data.engine_health,
                bodyHealth: data.body_health,
                fuel: data.fuel,
                mileage: data.mileage,
                locked: data.locked,
                engineOn: data.engine_on,
                modifications: JSON.parse(data.modifications || '{}'),
                insuranceExpires: data.insurance_expires,
                registrationExpires: data.registration_expires,
                isImpounded: data.is_impounded,
                impoundReason: data.impound_reason
            };
            this.vehicles.set(vehicle.id, extendedVehicle);
            return extendedVehicle;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to spawn vehicle from data:', error);
            return null;
        }
    }
    /**
     * Get vehicle by ID
     */
    getVehicle(vehicleId) {
        return this.vehicles.get(vehicleId);
    }
    /**
     * Get vehicles by owner
     */
    getVehiclesByOwner(ownerId) {
        return Array.from(this.vehicles.values()).filter(vehicle => vehicle.vehicleData?.ownerId === ownerId);
    }
    /**
     * Get all vehicles
     */
    getAllVehicles() {
        return Array.from(this.vehicles.values());
    }
    /**
     * Save vehicle data to database
     */
    async saveVehicle(vehicle) {
        if (!vehicle.vehicleData || !vehicle.vehicleData.id)
            return;
        try {
            await this.databaseManager.execute(`UPDATE vehicles SET 
                 position_x = ?, position_y = ?, position_z = ?, heading = ?,
                 engine_health = ?, body_health = ?, fuel = ?, mileage = ?,
                 locked = ?, engine_on = ?, modifications = ?
                 WHERE id = ?`, [
                vehicle.position.x, vehicle.position.y, vehicle.position.z, vehicle.heading,
                vehicle.vehicleData.engineHealth, vehicle.vehicleData.bodyHealth,
                vehicle.vehicleData.fuel, vehicle.vehicleData.mileage,
                vehicle.vehicleData.locked, vehicle.vehicleData.engineOn,
                JSON.stringify(vehicle.vehicleData.modifications),
                vehicle.vehicleData.id
            ]);
            Logger_1.Logger.debug(`Saved vehicle data: ${vehicle.vehicleData.model} (${vehicle.id})`);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to save vehicle ${vehicle.id}:`, error);
        }
    }
    /**
     * Save all vehicles
     */
    async saveAllVehicles() {
        Logger_1.Logger.info('💾 Saving all vehicles...');
        const savePromises = this.getAllVehicles().map(vehicle => this.saveVehicle(vehicle).catch(error => Logger_1.Logger.error(`Failed to save vehicle ${vehicle.id}:`, error)));
        await Promise.all(savePromises);
        Logger_1.Logger.success('✅ All vehicles saved');
    }
    /**
     * Delete vehicle
     */
    async deleteVehicle(vehicle) {
        try {
            // Remove from database
            if (vehicle.vehicleData?.id) {
                await this.databaseManager.execute('DELETE FROM vehicles WHERE id = ?', [vehicle.vehicleData.id]);
            }
            // Remove from game world
            this.vehicles.delete(vehicle.id);
            vehicle.destroy();
            Logger_1.Logger.info(`🗑️ Vehicle deleted: ${vehicle.vehicleData?.model} (${vehicle.id})`);
            await this.eventManager.emit('vehicle:deleted', vehicle);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to delete vehicle ${vehicle.id}:`, error);
        }
    }
    /**
     * Generate random number plate
     */
    generateNumberPlate() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    /**
     * Extend vehicle object
     */
    extendVehicle(vehicle) {
        const extendedVehicle = vehicle;
        extendedVehicle.fuelConsumptionRate = this.calculateFuelConsumption(vehicle.model);
        extendedVehicle.lastFuelUpdate = new Date();
        extendedVehicle.isBeingUsed = false;
        extendedVehicle.lastPosition = vehicle.position;
        return extendedVehicle;
    }
    /**
     * Calculate fuel consumption rate based on vehicle model
     */
    calculateFuelConsumption(model) {
        // Base consumption rate (liters per minute while driving)
        // This would be more sophisticated in a real implementation
        return 0.5; // 0.5 liters per minute
    }
    /**
     * Start fuel consumption system
     */
    startFuelSystem() {
        this.fuelUpdateInterval = setInterval(() => {
            this.updateVehicleFuel();
        }, 60000); // Update every minute
        Logger_1.Logger.info('⛽ Fuel system started');
    }
    /**
     * Update fuel for all vehicles
     */
    updateVehicleFuel() {
        for (const vehicle of this.vehicles.values()) {
            if (vehicle.engine && vehicle.vehicleData) {
                const timeDiff = (Date.now() - vehicle.lastFuelUpdate.getTime()) / 60000; // minutes
                const fuelConsumed = vehicle.fuelConsumptionRate * timeDiff;
                vehicle.vehicleData.fuel = Math.max(0, vehicle.vehicleData.fuel - fuelConsumed);
                vehicle.lastFuelUpdate = new Date();
                // Turn off engine if out of fuel
                if (vehicle.vehicleData.fuel <= 0 && vehicle.engine) {
                    vehicle.engine = false;
                    vehicle.vehicleData.engineOn = false;
                    // Notify occupants
                    const driver = vehicle.getOccupant(0);
                    if (driver) {
                        mp.events.call('client:notification', driver, ['Vehicle ran out of fuel!', 'error']);
                    }
                }
            }
        }
    }
    /**
     * Stop fuel system
     */
    stopFuelSystem() {
        if (this.fuelUpdateInterval) {
            clearInterval(this.fuelUpdateInterval);
            this.fuelUpdateInterval = null;
            Logger_1.Logger.info('⛽ Fuel system stopped');
        }
    }
    /**
     * Get vehicle count
     */
    getVehicleCount() {
        return this.vehicles.size;
    }
}
exports.VehicleManager = VehicleManager;
//# sourceMappingURL=VehicleManager.js.map