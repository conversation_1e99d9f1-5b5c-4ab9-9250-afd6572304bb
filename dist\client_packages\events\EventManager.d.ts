/**
 * Client-Side Event Manager
 * Advanced event handling system for client-side operations
 */
export type EventCallback = (...args: any[]) => void | Promise<void>;
export type EventMiddleware = (eventName: string, args: any[], next: () => void) => void | Promise<void>;
export interface EventListener {
    callback: EventCallback;
    once: boolean;
    priority: number;
}
export declare class EventManager {
    private events;
    private middlewares;
    private eventHistory;
    private maxHistorySize;
    /**
     * Add an event listener
     */
    on(eventName: string, callback: EventCallback, priority?: number): void;
    /**
     * Add a one-time event listener
     */
    once(eventName: string, callback: EventCallback, priority?: number): void;
    /**
     * Remove an event listener
     */
    off(eventName: string, callback?: EventCallback): void;
    /**
     * Remove all listeners for an event
     */
    removeAllListeners(eventName?: string): void;
    /**
     * Emit an event
     */
    emit(eventName: string, ...args: any[]): Promise<void>;
    /**
     * Add middleware
     */
    use(middleware: EventMiddleware): void;
    /**
     * Remove middleware
     */
    removeMiddleware(middleware: EventMiddleware): void;
    /**
     * Get event listeners count
     */
    getListenerCount(eventName: string): number;
    /**
     * Get all event names
     */
    getEventNames(): string[];
    /**
     * Check if event has listeners
     */
    hasListeners(eventName: string): boolean;
    /**
     * Get event history
     */
    getEventHistory(): Array<{
        name: string;
        args: any[];
        timestamp: Date;
    }>;
    /**
     * Clear event history
     */
    clearEventHistory(): void;
    /**
     * Wait for an event to be emitted
     */
    waitFor(eventName: string, timeout?: number): Promise<any[]>;
    /**
     * Create a namespaced event emitter
     */
    namespace(prefix: string): NamespacedEventManager;
    /**
     * Bind RAGE MP events to internal event system
     */
    bindRageEvent(rageName: string, internalName?: string): void;
    /**
     * Unbind RAGE MP events
     */
    unbindRageEvent(rageName: string): void;
    /**
     * Add event listener with options
     */
    private addEventListener;
    /**
     * Apply middlewares
     */
    private applyMiddlewares;
    /**
     * Add event to history
     */
    private addToHistory;
    /**
     * Get event statistics
     */
    getEventStats(): {
        [eventName: string]: number;
    };
}
/**
 * Namespaced Event Manager
 * Provides scoped event handling for client-side
 */
export declare class NamespacedEventManager {
    private eventManager;
    private prefix;
    constructor(eventManager: EventManager, prefix: string);
    on(eventName: string, callback: EventCallback, priority?: number): void;
    once(eventName: string, callback: EventCallback, priority?: number): void;
    off(eventName: string, callback?: EventCallback): void;
    emit(eventName: string, ...args: any[]): Promise<void>;
    getListenerCount(eventName: string): number;
    hasListeners(eventName: string): boolean;
    waitFor(eventName: string, timeout?: number): Promise<any[]>;
}
//# sourceMappingURL=EventManager.d.ts.map