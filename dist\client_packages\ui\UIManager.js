"use strict";
/**
 * UI Manager
 * Manages all UI interactions and state
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIManager = void 0;
const Logger_1 = require("../utils/Logger");
class UIManager {
    constructor(eventManager) {
        this.notificationCounter = 0;
        this.eventManager = eventManager;
        this.uiState = {
            isVisible: true,
            currentScreen: 'loading',
            notifications: [],
            modals: [],
            hudVisible: true,
            chatVisible: true,
            cursorVisible: false
        };
    }
    /**
     * Initialize UI Manager
     */
    async initialize() {
        try {
            Logger_1.Logger.info('🎨 Initializing UI Manager...');
            // Register UI events
            this.registerEvents();
            // Initialize cursor state
            this.setCursorVisible(false);
            Logger_1.Logger.success('✅ UI Manager initialized');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Failed to initialize UI Manager:', error);
            throw error;
        }
    }
    /**
     * Shutdown UI Manager
     */
    async shutdown() {
        try {
            Logger_1.Logger.info('🛑 Shutting down UI Manager...');
            // Clear all notifications
            this.clearAllNotifications();
            // Hide cursor
            this.setCursorVisible(false);
            Logger_1.Logger.success('✅ UI Manager shut down');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Error shutting down UI Manager:', error);
        }
    }
    /**
     * Update UI (called every frame)
     */
    update() {
        // Clean up expired notifications
        this.cleanupNotifications();
    }
    /**
     * Register UI events
     */
    registerEvents() {
        // Internal UI events
        this.eventManager.on('ui:showNotification', this.onShowNotification.bind(this));
        this.eventManager.on('ui:hideNotification', this.onHideNotification.bind(this));
        this.eventManager.on('ui:toggleHUD', this.onToggleHUD.bind(this));
        this.eventManager.on('ui:toggleChat', this.onToggleChat.bind(this));
        this.eventManager.on('ui:setCursor', this.onSetCursor.bind(this));
        Logger_1.Logger.debug('UI events registered');
    }
    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = {
            id: `notification_${++this.notificationCounter}`,
            message,
            type,
            duration,
            timestamp: new Date()
        };
        this.uiState.notifications.push(notification);
        // Emit to React components
        this.emitUIEvent('notification:add', notification);
        Logger_1.Logger.debug(`Notification shown: ${message} (${type})`);
        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification(notification.id);
            }, duration);
        }
    }
    /**
     * Hide notification
     */
    hideNotification(notificationId) {
        const index = this.uiState.notifications.findIndex(n => n.id === notificationId);
        if (index !== -1) {
            const notification = this.uiState.notifications.splice(index, 1)[0];
            this.emitUIEvent('notification:remove', notification);
            Logger_1.Logger.debug(`Notification hidden: ${notificationId}`);
        }
    }
    /**
     * Clear all notifications
     */
    clearAllNotifications() {
        this.uiState.notifications = [];
        this.emitUIEvent('notification:clear');
        Logger_1.Logger.debug('All notifications cleared');
    }
    /**
     * Show login form
     */
    showLoginForm(username) {
        this.uiState.currentScreen = 'login';
        this.setCursorVisible(true);
        this.emitUIEvent('auth:showLogin', { username });
        Logger_1.Logger.debug('Login form shown');
    }
    /**
     * Show register form
     */
    showRegisterForm() {
        this.uiState.currentScreen = 'register';
        this.setCursorVisible(true);
        this.emitUIEvent('auth:showRegister');
        Logger_1.Logger.debug('Register form shown');
    }
    /**
     * Show authentication error
     */
    showAuthError(message) {
        this.showNotification(message, 'error', 8000);
        this.emitUIEvent('auth:error', { message });
    }
    /**
     * Show character selection
     */
    showCharacterSelection(characters) {
        this.uiState.currentScreen = 'character-selection';
        this.setCursorVisible(true);
        this.emitUIEvent('character:showSelection', { characters });
        Logger_1.Logger.debug('Character selection shown');
    }
    /**
     * Show character error
     */
    showCharacterError(message) {
        this.showNotification(message, 'error', 8000);
        this.emitUIEvent('character:error', { message });
    }
    /**
     * Handle character loaded
     */
    onCharacterLoaded(characterData) {
        this.uiState.currentScreen = 'game';
        this.setCursorVisible(false);
        this.emitUIEvent('character:loaded', { characterData });
        this.showNotification(`Welcome back, ${characterData.name}!`, 'success');
        Logger_1.Logger.debug('Character loaded, entering game');
    }
    /**
     * Toggle HUD visibility
     */
    toggleHUD() {
        this.uiState.hudVisible = !this.uiState.hudVisible;
        this.emitUIEvent('hud:toggle', { visible: this.uiState.hudVisible });
        Logger_1.Logger.debug(`HUD ${this.uiState.hudVisible ? 'shown' : 'hidden'}`);
    }
    /**
     * Set HUD visibility
     */
    setHUDVisible(visible) {
        this.uiState.hudVisible = visible;
        this.emitUIEvent('hud:setVisible', { visible });
        Logger_1.Logger.debug(`HUD visibility set to: ${visible}`);
    }
    /**
     * Toggle chat visibility
     */
    toggleChat() {
        this.uiState.chatVisible = !this.uiState.chatVisible;
        this.emitUIEvent('chat:toggle', { visible: this.uiState.chatVisible });
        Logger_1.Logger.debug(`Chat ${this.uiState.chatVisible ? 'shown' : 'hidden'}`);
    }
    /**
     * Set cursor visibility
     */
    setCursorVisible(visible) {
        this.uiState.cursorVisible = visible;
        if (typeof mp !== 'undefined' && mp.gui && mp.gui.cursor) {
            mp.gui.cursor.show(visible, visible);
        }
        this.emitUIEvent('cursor:setVisible', { visible });
        Logger_1.Logger.debug(`Cursor visibility set to: ${visible}`);
    }
    /**
     * Open modal
     */
    openModal(modalName, data) {
        if (!this.uiState.modals.includes(modalName)) {
            this.uiState.modals.push(modalName);
            this.emitUIEvent('modal:open', { modalName, data });
            Logger_1.Logger.debug(`Modal opened: ${modalName}`);
        }
    }
    /**
     * Close modal
     */
    closeModal(modalName) {
        const index = this.uiState.modals.indexOf(modalName);
        if (index !== -1) {
            this.uiState.modals.splice(index, 1);
            this.emitUIEvent('modal:close', { modalName });
            Logger_1.Logger.debug(`Modal closed: ${modalName}`);
        }
    }
    /**
     * Close all modals
     */
    closeAllModals() {
        this.uiState.modals = [];
        this.emitUIEvent('modal:closeAll');
        Logger_1.Logger.debug('All modals closed');
    }
    /**
     * Event handlers
     */
    onShowNotification(data) {
        this.showNotification(data.message, data.type, data.duration);
    }
    onHideNotification(data) {
        this.hideNotification(data.id);
    }
    onToggleHUD() {
        this.toggleHUD();
    }
    onToggleChat() {
        this.toggleChat();
    }
    onSetCursor(data) {
        this.setCursorVisible(data.visible);
    }
    /**
     * Emit UI event to React components
     */
    emitUIEvent(eventName, data) {
        // Emit to internal event system
        this.eventManager.emit(`ui:${eventName}`, data);
        // Also emit to window for React components to listen
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent(`ui:${eventName}`, { detail: data }));
        }
    }
    /**
     * Clean up expired notifications
     */
    cleanupNotifications() {
        const now = Date.now();
        this.uiState.notifications = this.uiState.notifications.filter(notification => {
            if (notification.duration && notification.duration > 0) {
                const elapsed = now - notification.timestamp.getTime();
                return elapsed < notification.duration;
            }
            return true;
        });
    }
    /**
     * Get current UI state
     */
    getUIState() {
        return { ...this.uiState };
    }
    /**
     * Get notifications
     */
    getNotifications() {
        return [...this.uiState.notifications];
    }
    /**
     * Check if modal is open
     */
    isModalOpen(modalName) {
        return this.uiState.modals.includes(modalName);
    }
    /**
     * Get open modals
     */
    getOpenModals() {
        return [...this.uiState.modals];
    }
}
exports.UIManager = UIManager;
//# sourceMappingURL=UIManager.js.map