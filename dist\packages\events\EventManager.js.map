{"version": 3, "file": "EventManager.js", "sourceRoot": "", "sources": ["../../../packages/events/EventManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AAWzC,MAAa,YAAY;IAAzB;QACY,WAAM,GAAiC,IAAI,GAAG,EAAE,CAAC;QACjD,gBAAW,GAAsB,EAAE,CAAC;QACpC,iBAAY,GAA0D,EAAE,CAAC;QACzE,mBAAc,GAAW,IAAI,CAAC;IA2O1C,CAAC;IAzOG;;OAEG;IACI,EAAE,CAAC,SAAiB,EAAE,QAAuB,EAAE,WAAmB,CAAC;QACtE,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,SAAiB,EAAE,QAAuB,EAAE,WAAmB,CAAC;QACxE,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,SAAiB,EAAE,QAAwB;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;YAC9E,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,SAAkB;QACxC,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC9B,eAAM,CAAC,KAAK,CAAC,oCAAoC,SAAS,EAAE,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,IAAI,CAAC,SAAiB,EAAE,GAAG,IAAW;QAC/C,IAAI,CAAC;YACD,iBAAiB;YACjB,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAEnC,oBAAoB;YACpB,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAE7C,gBAAgB;YAChB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC7C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,eAAM,CAAC,KAAK,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC;gBACrD,OAAO;YACX,CAAC;YAED,2CAA2C;YAC3C,MAAM,eAAe,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YAE/E,oBAAoB;YACpB,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE,CAAC;gBACrC,IAAI,CAAC;oBACD,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;oBAEjC,qCAAqC;oBACrC,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAChB,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;wBAC1C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;4BACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;wBAC/B,CAAC;oBACL,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,eAAM,CAAC,KAAK,CAAC,+BAA+B,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;gBACrE,CAAC;YACL,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,kBAAkB,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,wBAAwB,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,UAA2B;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,eAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,UAA2B;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClC,eAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,SAAiB;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,SAAiB;QACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,eAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,SAAiB,EAAE,OAAgB;QAC9C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,SAAqC,CAAC;YAE1C,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;gBAChC,IAAI,SAAS,EAAE,CAAC;oBACZ,YAAY,CAAC,SAAS,CAAC,CAAC;gBAC5B,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE/B,IAAI,OAAO,EAAE,CAAC;gBACV,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;oBACxB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;oBAC9B,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,SAAS,kBAAkB,OAAO,IAAI,CAAC,CAAC,CAAC;gBACvE,CAAC,EAAE,OAAO,CAAC,CAAC;YAChB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAc;QAC3B,OAAO,IAAI,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,SAAiB,EAAE,QAAuB,EAAE,IAAa,EAAE,QAAgB;QAChG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QAC9C,SAAS,CAAC,IAAI,CAAC;YACX,QAAQ;YACR,IAAI;YACJ,QAAQ;SACX,CAAC,CAAC;QAEH,eAAM,CAAC,KAAK,CAAC,yBAAyB,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,IAAW;QACzD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACxC,IAAI,CAAC;oBACD,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;oBAC7B,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;oBAEjD,0BAA0B;oBAC1B,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;wBAC5B,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC/C,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,SAAiB,EAAE,IAAW;QAC/C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACnB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;CACJ;AA/OD,oCA+OC;AAED;;;GAGG;AACH,MAAa,sBAAsB;IAC/B,YACY,YAA0B,EAC1B,MAAc;QADd,iBAAY,GAAZ,YAAY,CAAc;QAC1B,WAAM,GAAN,MAAM,CAAQ;IACvB,CAAC;IAEG,EAAE,CAAC,SAAiB,EAAE,QAAuB,EAAE,WAAmB,CAAC;QACtE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC5E,CAAC;IAEM,IAAI,CAAC,SAAiB,EAAE,QAAuB,EAAE,WAAmB,CAAC;QACxE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAEM,GAAG,CAAC,SAAiB,EAAE,QAAwB;QAClD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,SAAiB,EAAE,GAAG,IAAW;QAC/C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;IACzE,CAAC;IAEM,gBAAgB,CAAC,SAAiB;QACrC,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;IAC7E,CAAC;IAEM,YAAY,CAAC,SAAiB;QACjC,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;IACzE,CAAC;IAEM,OAAO,CAAC,SAAiB,EAAE,OAAgB;QAC9C,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,EAAE,OAAO,CAAC,CAAC;IAC7E,CAAC;CACJ;AAjCD,wDAiCC"}