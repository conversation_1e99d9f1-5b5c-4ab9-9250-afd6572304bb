/**
 * Character System
 * Handles character creation, selection, and management
 */

import { Logger } from '../utils/Logger';
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';
import { ExtendedPlayer, CharacterData } from '../managers/PlayerManager';

export interface CharacterCreationData {
    name: string;
    age: number;
    gender: 'male' | 'female';
    skinData: any;
    clothesData: any;
}

export class CharacterSystem {
    private databaseManager: DatabaseManager;
    private eventManager: EventManager;
    private maxCharacters: number = 3;

    constructor() {
        this.databaseManager = new DatabaseManager();
        this.eventManager = new EventManager();
    }

    /**
     * Set dependencies (called by ServerCore)
     */
    public setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void {
        this.databaseManager = databaseManager;
        this.eventManager = eventManager;
    }

    /**
     * Handle character selection
     */
    public async selectCharacter(player: ExtendedPlayer, characterId: number): Promise<void> {
        try {
            if (!player.userData) {
                Logger.error(`Player ${player.name} tried to select character without being logged in`);
                return;
            }

            Logger.info(`🎭 Character selection: ${player.name} selecting character ${characterId}`);

            // Get character data
            const character = await this.getCharacterById(characterId, player.userData.id);
            if (!character) {
                this.sendCharacterError(player, 'Character not found or access denied');
                return;
            }

            // Load character
            await this.loadCharacter(player, character);

            Logger.success(`✅ Character loaded: ${character.name} for ${player.name}`);

        } catch (error) {
            Logger.error(`Failed to select character for ${player.name}:`, error);
            this.sendCharacterError(player, 'Failed to load character');
        }
    }

    /**
     * Handle character creation
     */
    public async createCharacter(player: ExtendedPlayer, data: CharacterCreationData): Promise<void> {
        try {
            if (!player.userData) {
                Logger.error(`Player ${player.name} tried to create character without being logged in`);
                return;
            }

            Logger.info(`🎭 Character creation: ${player.name} creating ${data.name}`);

            // Validate character data
            const validationError = this.validateCharacterData(data);
            if (validationError) {
                this.sendCharacterError(player, validationError);
                return;
            }

            // Check character limit
            const characterCount = await this.getCharacterCount(player.userData.id);
            if (characterCount >= this.maxCharacters) {
                this.sendCharacterError(player, `Maximum ${this.maxCharacters} characters allowed`);
                return;
            }

            // Check if name is already taken
            const existingCharacter = await this.getCharacterByName(data.name);
            if (existingCharacter) {
                this.sendCharacterError(player, 'Character name already exists');
                return;
            }

            // Create character
            const characterId = await this.createCharacterInDatabase(player.userData.id, data);
            if (!characterId) {
                this.sendCharacterError(player, 'Failed to create character');
                return;
            }

            // Load the new character
            const newCharacter = await this.getCharacterById(characterId, player.userData.id);
            if (newCharacter) {
                await this.loadCharacter(player, newCharacter);
            }

            Logger.success(`✅ Character created: ${data.name} for ${player.name}`);

        } catch (error) {
            Logger.error(`Failed to create character for ${player.name}:`, error);
            this.sendCharacterError(player, 'Failed to create character');
        }
    }

    /**
     * Handle character deletion
     */
    public async deleteCharacter(player: ExtendedPlayer, characterId: number): Promise<void> {
        try {
            if (!player.userData) {
                Logger.error(`Player ${player.name} tried to delete character without being logged in`);
                return;
            }

            Logger.info(`🗑️ Character deletion: ${player.name} deleting character ${characterId}`);

            // Get character data
            const character = await this.getCharacterById(characterId, player.userData.id);
            if (!character) {
                this.sendCharacterError(player, 'Character not found or access denied');
                return;
            }

            // Soft delete character (mark as inactive)
            await this.databaseManager.execute(
                'UPDATE characters SET is_active = FALSE WHERE id = ? AND user_id = ?',
                [characterId, player.userData.id]
            );

            // If this was the active character, clear it
            if (player.characterData?.id === characterId) {
                player.characterData = undefined;
                player.isCharacterSelected = false;
            }

            // Emit deletion event
            await this.eventManager.emit('character:deleted', player, character);

            // Refresh character selection
            this.showCharacterSelection(player);

            Logger.success(`✅ Character deleted: ${character.name} for ${player.name}`);

        } catch (error) {
            Logger.error(`Failed to delete character for ${player.name}:`, error);
            this.sendCharacterError(player, 'Failed to delete character');
        }
    }

    /**
     * Load character data and spawn player
     */
    private async loadCharacter(player: ExtendedPlayer, character: any): Promise<void> {
        try {
            // Set character data
            player.characterData = {
                id: character.id,
                userId: character.user_id,
                name: character.name,
                age: character.age,
                gender: character.gender,
                money: character.money,
                bankMoney: character.bank_money,
                position: {
                    x: character.position_x,
                    y: character.position_y,
                    z: character.position_z
                },
                heading: character.heading,
                dimension: character.dimension,
                interior: character.interior,
                health: character.health,
                armor: character.armor,
                hunger: character.hunger,
                thirst: character.thirst,
                stress: character.stress,
                job: character.job,
                jobRank: character.job_rank,
                jobSalary: character.job_salary,
                faction: character.faction,
                factionRank: character.faction_rank,
                skinData: JSON.parse(character.skin_data || '{}'),
                clothesData: JSON.parse(character.clothes_data || '{}'),
                isActive: character.is_active,
                playtime: character.playtime
            };

            player.isCharacterSelected = true;

            // Spawn player at character position
            const spawnPos = new mp.Vector3(
                character.position_x,
                character.position_y,
                character.position_z
            );

            player.spawn(spawnPos, character.heading);
            player.dimension = character.dimension;
            player.health = character.health;
            player.armour = character.armor;

            // Apply character appearance
            await this.applyCharacterAppearance(player, player.characterData);

            // Update last login
            await this.databaseManager.execute(
                'UPDATE characters SET updated_at = NOW() WHERE id = ?',
                [character.id]
            );

            // Send character data to client
            player.call('client:characterLoaded', [player.characterData]);

            // Emit character loaded event
            await this.eventManager.emit('character:loaded', player, player.characterData);

        } catch (error) {
            Logger.error(`Failed to load character for ${player.name}:`, error);
            throw error;
        }
    }

    /**
     * Apply character appearance
     */
    private async applyCharacterAppearance(player: ExtendedPlayer, character: CharacterData): Promise<void> {
        try {
            // Set player model based on gender
            const model = character.gender === 'male' ? mp.joaat('mp_m_freemode_01') : mp.joaat('mp_f_freemode_01');
            player.model = model;

            // Apply skin customization
            if (character.skinData && Object.keys(character.skinData).length > 0) {
                player.call('client:applySkinData', [character.skinData]);
            }

            // Apply clothes
            if (character.clothesData && Object.keys(character.clothesData).length > 0) {
                player.call('client:applyClothes', [character.clothesData]);
            }

            Logger.debug(`Applied appearance for character: ${character.name}`);

        } catch (error) {
            Logger.error(`Failed to apply character appearance for ${character.name}:`, error);
        }
    }

    /**
     * Show character selection screen
     */
    public async showCharacterSelection(player: ExtendedPlayer): Promise<void> {
        if (!player.userData) return;

        try {
            // Get player's characters
            const characters = await this.databaseManager.execute(
                'SELECT * FROM characters WHERE user_id = ? AND is_active = TRUE ORDER BY created_at ASC',
                [player.userData.id]
            );

            // Send character data to client
            player.call('client:showCharacterSelection', [characters || []]);

        } catch (error) {
            Logger.error(`Failed to show character selection for ${player.name}:`, error);
        }
    }

    /**
     * Get character by ID
     */
    private async getCharacterById(characterId: number, userId: number): Promise<any> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT * FROM characters WHERE id = ? AND user_id = ? AND is_active = TRUE',
                [characterId, userId]
            );

            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.error('Failed to get character by ID:', error);
            return null;
        }
    }

    /**
     * Get character by name
     */
    private async getCharacterByName(name: string): Promise<any> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT * FROM characters WHERE name = ? AND is_active = TRUE',
                [name]
            );

            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            Logger.error('Failed to get character by name:', error);
            return null;
        }
    }

    /**
     * Get character count for user
     */
    private async getCharacterCount(userId: number): Promise<number> {
        try {
            const result = await this.databaseManager.execute(
                'SELECT COUNT(*) as count FROM characters WHERE user_id = ? AND is_active = TRUE',
                [userId]
            );

            return result && result.length > 0 ? result[0].count : 0;
        } catch (error) {
            Logger.error('Failed to get character count:', error);
            return 0;
        }
    }

    /**
     * Create character in database
     */
    private async createCharacterInDatabase(userId: number, data: CharacterCreationData): Promise<number | null> {
        try {
            const result = await this.databaseManager.execute(
                `INSERT INTO characters (
                    user_id, name, age, gender, money, bank_money,
                    position_x, position_y, position_z, heading, dimension,
                    health, armor, hunger, thirst, stress,
                    skin_data, clothes_data, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
                [
                    userId, data.name, data.age, data.gender, 5000, 0,
                    -1037.8, -2738.5, 20.2, 0.0, 0,
                    100, 0, 100, 100, 0,
                    JSON.stringify(data.skinData), JSON.stringify(data.clothesData)
                ]
            );

            return result && result.insertId ? result.insertId : null;
        } catch (error) {
            Logger.error('Failed to create character in database:', error);
            return null;
        }
    }

    /**
     * Validate character creation data
     */
    private validateCharacterData(data: CharacterCreationData): string | null {
        if (!data.name || data.name.length < 2 || data.name.length > 50) {
            return 'Character name must be between 2 and 50 characters';
        }

        if (!/^[a-zA-Z\s]+$/.test(data.name)) {
            return 'Character name can only contain letters and spaces';
        }

        if (!data.age || data.age < 18 || data.age > 100) {
            return 'Character age must be between 18 and 100';
        }

        if (!data.gender || !['male', 'female'].includes(data.gender)) {
            return 'Invalid gender selection';
        }

        return null;
    }

    /**
     * Send character error to player
     */
    private sendCharacterError(player: ExtendedPlayer, message: string): void {
        player.call('client:characterError', [message]);
        Logger.warn(`Character error for ${player.name}: ${message}`);
    }

    /**
     * Save character data
     */
    public async saveCharacterData(player: ExtendedPlayer): Promise<void> {
        if (!player.characterData) return;

        try {
            await this.databaseManager.execute(
                `UPDATE characters SET 
                 money = ?, bank_money = ?,
                 position_x = ?, position_y = ?, position_z = ?, heading = ?,
                 dimension = ?, interior = ?,
                 health = ?, armor = ?,
                 hunger = ?, thirst = ?, stress = ?,
                 updated_at = NOW()
                 WHERE id = ?`,
                [
                    player.characterData.money, player.characterData.bankMoney,
                    player.position.x, player.position.y, player.position.z, player.heading,
                    player.dimension, player.characterData.interior,
                    player.health, player.armour,
                    player.characterData.hunger, player.characterData.thirst, player.characterData.stress,
                    player.characterData.id
                ]
            );

            Logger.debug(`Saved character data for: ${player.characterData.name}`);
        } catch (error) {
            Logger.error(`Failed to save character data for ${player.name}:`, error);
        }
    }
}
