/**
 * Authentication System
 * Handles user registration, login, and session management
 */
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';
import { ExtendedPlayer } from '../managers/PlayerManager';
export interface LoginData {
    username: string;
    password: string;
}
export interface RegisterData {
    username: string;
    email: string;
    password: string;
    confirmPassword: string;
}
export declare class AuthenticationSystem {
    private databaseManager;
    private eventManager;
    private maxLoginAttempts;
    private lockoutDuration;
    constructor();
    /**
     * Set dependencies (called by ServerCore)
     */
    setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void;
    /**
     * Show login screen to player
     */
    showLoginScreen(player: ExtendedPlayer): Promise<void>;
    /**
     * Handle player login attempt
     */
    handleLogin(player: ExtendedPlayer, data: LoginData): Promise<void>;
    /**
     * Handle player registration
     */
    handleRegister(player: ExtendedPlayer, data: RegisterData): Promise<void>;
    /**
     * Handle successful login
     */
    private handleSuccessfulLogin;
    /**
     * <PERSON><PERSON> failed login
     */
    private handleFailedLogin;
    /**
     * Show character selection screen
     */
    private showCharacterSelection;
    /**
     * Check if player already has an account
     */
    private checkExistingUser;
    /**
     * Validate login data
     */
    private validateLoginData;
    /**
     * Validate registration data
     */
    private validateRegisterData;
    /**
     * Check if player is locked out
     */
    private isPlayerLockedOut;
    /**
     * Get user by username
     */
    private getUserByUsername;
    /**
     * Get user by email
     */
    private getUserByEmail;
    /**
     * Get user by social club
     */
    private getUserBySocialClub;
    /**
     * Get user by ID
     */
    private getUserById;
    /**
     * Hash password
     */
    private hashPassword;
    /**
     * Verify password
     */
    private verifyPassword;
    /**
     * Create user account
     */
    private createUserAccount;
    /**
     * Send authentication error to player
     */
    private sendAuthError;
}
//# sourceMappingURL=AuthenticationSystem.d.ts.map