"use strict";
/**
 * UI Context Provider
 * Provides UI state and actions to React components
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.useUI = exports.UIProvider = void 0;
const react_1 = __importStar(require("react"));
const initialState = {
    isLoading: true,
    currentScreen: 'loading',
    notifications: [],
    modals: [],
    hudVisible: true,
    chatVisible: true,
    cursorVisible: false,
    characterData: null,
    userData: null
};
const uiReducer = (state, action) => {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, isLoading: action.payload };
        case 'SET_CURRENT_SCREEN':
            return { ...state, currentScreen: action.payload };
        case 'ADD_NOTIFICATION':
            return {
                ...state,
                notifications: [...state.notifications, action.payload]
            };
        case 'REMOVE_NOTIFICATION':
            return {
                ...state,
                notifications: state.notifications.filter(n => n.id !== action.payload)
            };
        case 'CLEAR_NOTIFICATIONS':
            return { ...state, notifications: [] };
        case 'OPEN_MODAL':
            return {
                ...state,
                modals: state.modals.includes(action.payload)
                    ? state.modals
                    : [...state.modals, action.payload]
            };
        case 'CLOSE_MODAL':
            return {
                ...state,
                modals: state.modals.filter(m => m !== action.payload)
            };
        case 'SET_HUD_VISIBLE':
            return { ...state, hudVisible: action.payload };
        case 'SET_CHAT_VISIBLE':
            return { ...state, chatVisible: action.payload };
        case 'SET_CURSOR_VISIBLE':
            return { ...state, cursorVisible: action.payload };
        case 'SET_CHARACTER_DATA':
            return { ...state, characterData: action.payload };
        case 'SET_USER_DATA':
            return { ...state, userData: action.payload };
        default:
            return state;
    }
};
const UIContext = (0, react_1.createContext)(null);
const UIProvider = ({ children }) => {
    const [state, dispatch] = (0, react_1.useReducer)(uiReducer, initialState);
    const actions = {
        setLoading: (loading) => dispatch({ type: 'SET_LOADING', payload: loading }),
        setCurrentScreen: (screen) => dispatch({ type: 'SET_CURRENT_SCREEN', payload: screen }),
        addNotification: (notification) => dispatch({ type: 'ADD_NOTIFICATION', payload: notification }),
        removeNotification: (id) => dispatch({ type: 'REMOVE_NOTIFICATION', payload: id }),
        clearNotifications: () => dispatch({ type: 'CLEAR_NOTIFICATIONS' }),
        openModal: (modalName) => dispatch({ type: 'OPEN_MODAL', payload: modalName }),
        closeModal: (modalName) => dispatch({ type: 'CLOSE_MODAL', payload: modalName }),
        setHUDVisible: (visible) => dispatch({ type: 'SET_HUD_VISIBLE', payload: visible }),
        setChatVisible: (visible) => dispatch({ type: 'SET_CHAT_VISIBLE', payload: visible }),
        setCursorVisible: (visible) => dispatch({ type: 'SET_CURSOR_VISIBLE', payload: visible }),
        setCharacterData: (data) => dispatch({ type: 'SET_CHARACTER_DATA', payload: data }),
        setUserData: (data) => dispatch({ type: 'SET_USER_DATA', payload: data })
    };
    // Listen for UI events from the game
    (0, react_1.useEffect)(() => {
        const handleUIEvent = (event) => {
            const { type, detail } = event;
            switch (type) {
                case 'ui:notification:add':
                    actions.addNotification(detail);
                    break;
                case 'ui:notification:remove':
                    actions.removeNotification(detail.id);
                    break;
                case 'ui:notification:clear':
                    actions.clearNotifications();
                    break;
                case 'ui:auth:showLogin':
                    actions.setCurrentScreen('auth');
                    actions.setCursorVisible(true);
                    break;
                case 'ui:auth:showRegister':
                    actions.setCurrentScreen('auth');
                    actions.setCursorVisible(true);
                    break;
                case 'ui:character:showSelection':
                    actions.setCurrentScreen('character-selection');
                    actions.setCursorVisible(true);
                    break;
                case 'ui:character:loaded':
                    actions.setCharacterData(detail.characterData);
                    actions.setCurrentScreen('game');
                    actions.setCursorVisible(false);
                    break;
                case 'ui:hud:toggle':
                    actions.setHUDVisible(detail.visible);
                    break;
                case 'ui:chat:toggle':
                    actions.setChatVisible(detail.visible);
                    break;
                case 'ui:cursor:setVisible':
                    actions.setCursorVisible(detail.visible);
                    break;
                case 'ui:modal:open':
                    actions.openModal(detail.modalName);
                    break;
                case 'ui:modal:close':
                    actions.closeModal(detail.modalName);
                    break;
            }
        };
        // Add event listeners for all UI events
        const eventTypes = [
            'ui:notification:add',
            'ui:notification:remove',
            'ui:notification:clear',
            'ui:auth:showLogin',
            'ui:auth:showRegister',
            'ui:character:showSelection',
            'ui:character:loaded',
            'ui:hud:toggle',
            'ui:chat:toggle',
            'ui:cursor:setVisible',
            'ui:modal:open',
            'ui:modal:close'
        ];
        eventTypes.forEach(eventType => {
            window.addEventListener(eventType, handleUIEvent);
        });
        return () => {
            eventTypes.forEach(eventType => {
                window.removeEventListener(eventType, handleUIEvent);
            });
        };
    }, []);
    return (<UIContext.Provider value={{ state, actions }}>
            {children}
        </UIContext.Provider>);
};
exports.UIProvider = UIProvider;
const useUI = () => {
    const context = (0, react_1.useContext)(UIContext);
    if (!context) {
        throw new Error('useUI must be used within a UIProvider');
    }
    return context;
};
exports.useUI = useUI;
exports.default = UIContext;
//# sourceMappingURL=UIContext.js.map