{"name": "ragemp-advanced-roleplay", "version": "1.0.0", "description": "Advanced Roleplay Server for RAGE Multiplayer built with TypeScript and React", "main": "packages/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "npm run build:watch", "start": "node packages/index.js", "lint": "eslint packages/**/*.ts client_packages/**/*.ts --fix", "format": "prettier --write packages/**/*.ts client_packages/**/*.ts", "clean": "rimraf packages/**/*.js client_packages/**/*.js", "build:client": "webpack --config webpack.config.js", "build:client:watch": "webpack --config webpack.config.js --watch", "build:all": "npm run build && npm run build:client", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["ragemp", "roleplay", "typescript", "react", "multiplayer", "gta5"], "author": "Advanced Roleplay Team", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.3.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^9.0.7", "bcrypt": "^5.1.1", "css-loader": "^7.1.2", "mysql2": "^3.6.5", "react": "^19.1.0", "react-dom": "^19.1.0", "style-loader": "^4.0.0", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0"}}