"use strict";
/**
 * RAGE MP Advanced Roleplay Server
 * Main Server Entry Point
 *
 * <AUTHOR> Name
 * @version 1.0.0
 * @description Advanced roleplay server built with TypeScript
 */
Object.defineProperty(exports, "__esModule", { value: true });
const ServerCore_1 = require("./core/ServerCore");
const Logger_1 = require("./utils/Logger");
// Initialize server core
const server = new ServerCore_1.ServerCore();
// Start the server
server.start().then(() => {
    Logger_1.Logger.info('🚀 Advanced Roleplay Server started successfully!');
}).catch((error) => {
    Logger_1.Logger.error('❌ Failed to start server:', error);
    process.exit(1);
});
// Graceful shutdown
process.on('SIGINT', async () => {
    Logger_1.Logger.info('🛑 Shutting down server...');
    await server.shutdown();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    Logger_1.Logger.info('🛑 Shutting down server...');
    await server.shutdown();
    process.exit(0);
});
//# sourceMappingURL=index.js.map