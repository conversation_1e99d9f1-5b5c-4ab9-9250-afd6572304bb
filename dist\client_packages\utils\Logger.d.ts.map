{"version": 3, "file": "Logger.d.ts", "sourceRoot": "", "sources": ["../../../client_packages/utils/Logger.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,oBAAY,QAAQ;IAChB,KAAK,IAAI;IACT,IAAI,IAAI;IACR,IAAI,IAAI;IACR,KAAK,IAAI;IACT,OAAO,IAAI;CACd;AAED,MAAM,WAAW,QAAQ;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,QAAQ,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,MAAM,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,qBAAa,MAAM;IACf,OAAO,CAAC,MAAM,CAAC,QAAQ,CAA2B;IAClD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAkB;IACrC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAe;IACrC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAiB;IAE7C;;OAEG;WACW,WAAW,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI;IAIhD;;OAEG;WACW,gBAAgB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAItD;;OAEG;WACW,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAIvE;;OAEG;WACW,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAItE;;OAEG;WACW,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAItE;;OAEG;WACW,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAIvE;;OAEG;WACW,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAIzE;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,GAAG;IA6BlB;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,eAAe;IAiC9B;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,YAAY;IAiB3B;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,cAAc;IAiB7B;;OAEG;WACW,OAAO,IAAI,QAAQ,EAAE;IAInC;;OAEG;WACW,cAAc,CAAC,KAAK,EAAE,QAAQ,GAAG,QAAQ,EAAE;IAIzD;;OAEG;WACW,SAAS,IAAI,IAAI;IAI/B;;OAEG;WACW,UAAU,IAAI,MAAM;IAelC;;OAEG;WACW,YAAY,CAAC,KAAK,GAAE,MAAY,GAAG,QAAQ,EAAE;IAI3D;;OAEG;WACW,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,QAAQ,EAAE;IASxD;;OAEG;WACW,WAAW,IAAI;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE;CAgCzD"}