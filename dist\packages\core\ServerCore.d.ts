/**
 * Server Core - Main server management class
 * Handles initialization, module loading, and server lifecycle
 */
import { ConfigManager } from '../config/ConfigManager';
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';
import { PlayerManager } from '../managers/PlayerManager';
import { VehicleManager } from '../managers/VehicleManager';
import { AuthenticationSystem } from '../systems/AuthenticationSystem';
import { CharacterSystem } from '../systems/CharacterSystem';
export declare class ServerCore {
    private static instance;
    private isInitialized;
    private isRunning;
    private configManager;
    private databaseManager;
    private eventManager;
    private playerManager;
    private vehicleManager;
    private authSystem;
    private characterSystem;
    constructor();
    /**
     * Initialize all server components
     */
    start(): Promise<void>;
    /**
     * Shutdown server gracefully
     */
    shutdown(): Promise<void>;
    /**
     * Register all server events
     */
    private registerEvents;
    /**
     * Handle player join
     */
    private onPlayerJoin;
    /**
     * Handle player quit
     */
    private onPlayerQuit;
    /**
     * Handle player ready
     */
    private onPlayerReady;
    /**
     * Handle player death
     */
    private onPlayerDeath;
    /**
     * Handle player login
     */
    private onPlayerLogin;
    /**
     * Handle player registration
     */
    private onPlayerRegister;
    static getInstance(): ServerCore;
    getConfigManager(): ConfigManager;
    getDatabaseManager(): DatabaseManager;
    getEventManager(): EventManager;
    getPlayerManager(): PlayerManager;
    getVehicleManager(): VehicleManager;
    getAuthSystem(): AuthenticationSystem;
    getCharacterSystem(): CharacterSystem;
}
//# sourceMappingURL=ServerCore.d.ts.map