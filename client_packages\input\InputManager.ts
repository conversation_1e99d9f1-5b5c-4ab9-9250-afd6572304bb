/**
 * Input Manager
 * Handles keyboard and mouse input for the client
 */

import { Logger } from '../utils/Logger';
import { EventManager } from '../events/EventManager';

export interface KeyBinding {
    key: number;
    callback: () => void;
    description: string;
    category: string;
}

export class InputManager {
    private eventManager: EventManager;
    private keyBindings: Map<number, KeyBinding[]> = new Map();
    private isEnabled: boolean = true;

    constructor(eventManager: EventManager) {
        this.eventManager = eventManager;
    }

    /**
     * Initialize Input Manager
     */
    public async initialize(): Promise<void> {
        try {
            Logger.info('⌨️ Initializing Input Manager...');

            // Register default key bindings
            this.registerDefaultBindings();

            Logger.success('✅ Input Manager initialized');
        } catch (error) {
            Logger.error('❌ Failed to initialize Input Manager:', error);
            throw error;
        }
    }

    /**
     * Shutdown Input Manager
     */
    public async shutdown(): Promise<void> {
        try {
            Logger.info('🛑 Shutting down Input Manager...');
            
            // Clear all key bindings
            this.clearAllBindings();
            
            Logger.success('✅ Input Manager shut down');
        } catch (error) {
            Logger.error('❌ Error shutting down Input Manager:', error);
        }
    }

    /**
     * Register default key bindings
     */
    private registerDefaultBindings(): void {
        // Example key bindings
        this.bindKey(113, () => { // F2
            this.eventManager.emit('ui:toggleDebug');
        }, 'Toggle Debug Panel', 'Debug');

        this.bindKey(114, () => { // F3
            this.eventManager.emit('ui:toggleHUD');
        }, 'Toggle HUD', 'UI');

        this.bindKey(115, () => { // F4
            this.eventManager.emit('ui:toggleChat');
        }, 'Toggle Chat', 'UI');
    }

    /**
     * Bind a key to a callback
     */
    public bindKey(keyCode: number, callback: () => void, description: string = '', category: string = 'General'): void {
        if (!this.keyBindings.has(keyCode)) {
            this.keyBindings.set(keyCode, []);
        }

        const binding: KeyBinding = {
            key: keyCode,
            callback,
            description,
            category
        };

        this.keyBindings.get(keyCode)!.push(binding);

        // Register with RAGE MP
        if (typeof mp !== 'undefined' && mp.keys) {
            mp.keys.bind(keyCode, true, callback);
        }

        Logger.debug(`Key bound: ${keyCode} - ${description}`);
    }

    /**
     * Unbind a key
     */
    public unbindKey(keyCode: number, callback?: () => void): void {
        const bindings = this.keyBindings.get(keyCode);
        if (!bindings) return;

        if (callback) {
            const index = bindings.findIndex(binding => binding.callback === callback);
            if (index !== -1) {
                bindings.splice(index, 1);
                
                // Unregister with RAGE MP
                if (typeof mp !== 'undefined' && mp.keys) {
                    mp.keys.unbind(keyCode, true, callback);
                }
            }
        } else {
            // Remove all bindings for this key
            bindings.forEach(binding => {
                if (typeof mp !== 'undefined' && mp.keys) {
                    mp.keys.unbind(keyCode, true, binding.callback);
                }
            });
            this.keyBindings.delete(keyCode);
        }

        Logger.debug(`Key unbound: ${keyCode}`);
    }

    /**
     * Clear all key bindings
     */
    public clearAllBindings(): void {
        this.keyBindings.forEach((bindings, keyCode) => {
            bindings.forEach(binding => {
                if (typeof mp !== 'undefined' && mp.keys) {
                    mp.keys.unbind(keyCode, true, binding.callback);
                }
            });
        });

        this.keyBindings.clear();
        Logger.debug('All key bindings cleared');
    }

    /**
     * Get all key bindings
     */
    public getKeyBindings(): Map<number, KeyBinding[]> {
        return new Map(this.keyBindings);
    }

    /**
     * Enable/disable input processing
     */
    public setEnabled(enabled: boolean): void {
        this.isEnabled = enabled;
        Logger.debug(`Input Manager ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Check if input is enabled
     */
    public isInputEnabled(): boolean {
        return this.isEnabled;
    }
}
