{"version": 3, "file": "Logger.js", "sourceRoot": "", "sources": ["../../../client_packages/utils/Logger.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,IAAY,QAMX;AAND,WAAY,QAAQ;IAChB,yCAAS,CAAA;IACT,uCAAQ,CAAA;IACR,uCAAQ,CAAA;IACR,yCAAS,CAAA;IACT,6CAAW,CAAA;AACf,CAAC,EANW,QAAQ,wBAAR,QAAQ,QAMnB;AAUD,MAAa,MAAM;IAMf;;OAEG;IACI,MAAM,CAAC,WAAW,CAAC,KAAe;QACrC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,OAAgB;QAC3C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,OAAe,EAAE,IAAU,EAAE,MAAe;QAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,IAAI,CAAC,OAAe,EAAE,IAAU,EAAE,MAAe;QAC3D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,IAAI,CAAC,OAAe,EAAE,IAAU,EAAE,MAAe;QAC3D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,OAAe,EAAE,IAAU,EAAE,MAAe;QAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO,CAAC,OAAe,EAAE,IAAU,EAAE,MAAe;QAC9D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,GAAG,CAAC,KAAe,EAAE,OAAe,EAAE,IAAU,EAAE,MAAe;QAC5E,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ;YAAE,OAAO;QAElC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAa;YACvB,SAAS;YACT,KAAK;YACL,OAAO;YACP,IAAI;YACJ,MAAM;SACT,CAAC;QAEF,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzB,0BAA0B;QAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;QAED,+BAA+B;QAC/B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;QAED,mDAAmD;QACnD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,KAAe;QAC1C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnF,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAE3D,IAAI,OAAO,GAAG,IAAI,SAAS,KAAK,QAAQ,GAAG,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAExE,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YACb,OAAO,IAAI,UAAU,CAAC;QAC1B,CAAC;QAED,gDAAgD;QAChD,QAAQ,KAAK,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,QAAQ,CAAC,KAAK;gBACf,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,QAAQ,CAAC,IAAI;gBACd,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;gBACxC,MAAM;YACV,KAAK,QAAQ,CAAC,IAAI;gBACd,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;gBACxC,MAAM;YACV,KAAK,QAAQ,CAAC,KAAK;gBACf,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,QAAQ,CAAC,OAAO;gBACjB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;gBACvC,MAAM;YACV;gBACI,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CAAC,KAAe;QACvC,mDAAmD;QACnD,IAAI,KAAK,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACD,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC;oBACpD,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;oBAC5B,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;iBAC3C,CAAC,CAAC,CAAC;YACR,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,yDAAyD;YAC7D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,KAAe;QACzC,QAAQ,KAAK,EAAE,CAAC;YACZ,KAAK,QAAQ,CAAC,KAAK;gBACf,OAAO,SAAS,CAAC;YACrB,KAAK,QAAQ,CAAC,IAAI;gBACd,OAAO,QAAQ,CAAC;YACpB,KAAK,QAAQ,CAAC,IAAI;gBACd,OAAO,QAAQ,CAAC;YACpB,KAAK,QAAQ,CAAC,KAAK;gBACf,OAAO,SAAS,CAAC;YACrB,KAAK,QAAQ,CAAC,OAAO;gBACjB,OAAO,WAAW,CAAC;YACvB;gBACI,OAAO,WAAW,CAAC;QAC3B,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO;QACjB,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,KAAe;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS;QACnB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACvB,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACvD,IAAI,OAAO,GAAG,IAAI,SAAS,KAAK,QAAQ,GAAG,SAAS,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAEtE,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBACX,OAAO,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACtD,CAAC;YAED,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,QAAgB,GAAG;QAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU,CAAC,UAAkB;QACvC,MAAM,IAAI,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAC1B,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;YACxC,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CACtE,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW;QACrB,MAAM,KAAK,GAA8B;YACrC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;YACvB,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,QAAQ,GAAG,CAAC,KAAK,EAAE,CAAC;gBAChB,KAAK,QAAQ,CAAC,KAAK;oBACf,KAAK,CAAC,KAAK,EAAE,CAAC;oBACd,MAAM;gBACV,KAAK,QAAQ,CAAC,IAAI;oBACd,KAAK,CAAC,IAAI,EAAE,CAAC;oBACb,MAAM;gBACV,KAAK,QAAQ,CAAC,IAAI;oBACd,KAAK,CAAC,IAAI,EAAE,CAAC;oBACb,MAAM;gBACV,KAAK,QAAQ,CAAC,KAAK;oBACf,KAAK,CAAC,KAAK,EAAE,CAAC;oBACd,MAAM;gBACV,KAAK,QAAQ,CAAC,OAAO;oBACjB,KAAK,CAAC,OAAO,EAAE,CAAC;oBAChB,MAAM;YACd,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACjB,CAAC;;AA/PL,wBAgQC;AA/PkB,eAAQ,GAAa,QAAQ,CAAC,IAAI,CAAC;AACnC,WAAI,GAAe,EAAE,CAAC;AACtB,cAAO,GAAW,GAAG,CAAC,CAAC,2BAA2B;AAClD,oBAAa,GAAY,IAAI,CAAC"}