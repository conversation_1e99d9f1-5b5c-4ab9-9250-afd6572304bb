"use strict";
/**
 * Advanced Logger System
 * Provides comprehensive logging functionality with different levels and formatting
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
    LogLevel[LogLevel["SUCCESS"] = 4] = "SUCCESS";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    /**
     * Set the minimum log level
     */
    static setLogLevel(level) {
        this.logLevel = level;
    }
    /**
     * Debug level logging
     */
    static debug(message, data, source) {
        this.log(LogLevel.DEBUG, message, data, source);
    }
    /**
     * Info level logging
     */
    static info(message, data, source) {
        this.log(LogLevel.INFO, message, data, source);
    }
    /**
     * Warning level logging
     */
    static warn(message, data, source) {
        this.log(LogLevel.WARN, message, data, source);
    }
    /**
     * Error level logging
     */
    static error(message, data, source) {
        this.log(LogLevel.ERROR, message, data, source);
    }
    /**
     * Success level logging
     */
    static success(message, data, source) {
        this.log(LogLevel.SUCCESS, message, data, source);
    }
    /**
     * Core logging method
     */
    static log(level, message, data, source) {
        if (level < this.logLevel)
            return;
        const timestamp = new Date();
        const logEntry = {
            timestamp,
            level,
            message,
            data,
            source
        };
        // Add to logs array
        this.logs.push(logEntry);
        // Maintain max logs limit
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        // Format and output to console
        const formattedMessage = this.formatMessage(logEntry);
        console.log(formattedMessage);
    }
    /**
     * Format log message for console output
     */
    static formatMessage(entry) {
        const timestamp = entry.timestamp.toISOString().replace('T', ' ').substring(0, 19);
        const levelStr = this.getLevelString(entry.level);
        const sourceStr = entry.source ? ` [${entry.source}]` : '';
        let message = `[${timestamp}] ${levelStr}${sourceStr} ${entry.message}`;
        if (entry.data) {
            message += ` | Data: ${JSON.stringify(entry.data)}`;
        }
        return message;
    }
    /**
     * Get colored level string
     */
    static getLevelString(level) {
        switch (level) {
            case LogLevel.DEBUG:
                return '\x1b[36m[DEBUG]\x1b[0m'; // Cyan
            case LogLevel.INFO:
                return '\x1b[34m[INFO]\x1b[0m'; // Blue
            case LogLevel.WARN:
                return '\x1b[33m[WARN]\x1b[0m'; // Yellow
            case LogLevel.ERROR:
                return '\x1b[31m[ERROR]\x1b[0m'; // Red
            case LogLevel.SUCCESS:
                return '\x1b[32m[SUCCESS]\x1b[0m'; // Green
            default:
                return '[UNKNOWN]';
        }
    }
    /**
     * Get all logs
     */
    static getLogs() {
        return [...this.logs];
    }
    /**
     * Get logs by level
     */
    static getLogsByLevel(level) {
        return this.logs.filter(log => log.level === level);
    }
    /**
     * Clear all logs
     */
    static clearLogs() {
        this.logs = [];
    }
    /**
     * Export logs to string
     */
    static exportLogs() {
        return this.logs.map(log => this.formatMessage(log)).join('\n');
    }
}
exports.Logger = Logger;
Logger.logLevel = LogLevel.INFO;
Logger.logs = [];
Logger.maxLogs = 1000;
//# sourceMappingURL=Logger.js.map