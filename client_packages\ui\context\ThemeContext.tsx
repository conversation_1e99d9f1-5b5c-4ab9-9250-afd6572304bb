/**
 * Theme Context Provider
 * Provides theme configuration and styling to React components
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Theme {
    name: string;
    colors: {
        primary: string;
        secondary: string;
        accent: string;
        background: string;
        surface: string;
        text: {
            primary: string;
            secondary: string;
            disabled: string;
        };
        status: {
            success: string;
            warning: string;
            error: string;
            info: string;
        };
        border: string;
        shadow: string;
    };
    typography: {
        fontFamily: string;
        fontSize: {
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            xxl: string;
        };
        fontWeight: {
            light: number;
            normal: number;
            medium: number;
            bold: number;
        };
    };
    spacing: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        xxl: string;
    };
    borderRadius: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        full: string;
    };
    animation: {
        duration: {
            fast: string;
            normal: string;
            slow: string;
        };
        easing: {
            ease: string;
            easeIn: string;
            easeOut: string;
            easeInOut: string;
        };
    };
}

const darkTheme: Theme = {
    name: 'dark',
    colors: {
        primary: '#3B82F6',
        secondary: '#6366F1',
        accent: '#F59E0B',
        background: '#0F172A',
        surface: '#1E293B',
        text: {
            primary: '#F8FAFC',
            secondary: '#CBD5E1',
            disabled: '#64748B'
        },
        status: {
            success: '#10B981',
            warning: '#F59E0B',
            error: '#EF4444',
            info: '#3B82F6'
        },
        border: '#334155',
        shadow: 'rgba(0, 0, 0, 0.5)'
    },
    typography: {
        fontFamily: '"Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
        fontSize: {
            xs: '0.75rem',
            sm: '0.875rem',
            md: '1rem',
            lg: '1.125rem',
            xl: '1.25rem',
            xxl: '1.5rem'
        },
        fontWeight: {
            light: 300,
            normal: 400,
            medium: 500,
            bold: 700
        }
    },
    spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        xxl: '3rem'
    },
    borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        xl: '0.75rem',
        full: '9999px'
    },
    animation: {
        duration: {
            fast: '150ms',
            normal: '300ms',
            slow: '500ms'
        },
        easing: {
            ease: 'ease',
            easeIn: 'ease-in',
            easeOut: 'ease-out',
            easeInOut: 'ease-in-out'
        }
    }
};

const lightTheme: Theme = {
    ...darkTheme,
    name: 'light',
    colors: {
        ...darkTheme.colors,
        background: '#FFFFFF',
        surface: '#F8FAFC',
        text: {
            primary: '#0F172A',
            secondary: '#475569',
            disabled: '#94A3B8'
        },
        border: '#E2E8F0',
        shadow: 'rgba(0, 0, 0, 0.1)'
    }
};

export interface ThemeContextType {
    theme: Theme;
    themeName: 'dark' | 'light';
    setTheme: (themeName: 'dark' | 'light') => void;
    toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | null>(null);

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [themeName, setThemeName] = useState<'dark' | 'light'>('dark');

    const theme = themeName === 'dark' ? darkTheme : lightTheme;

    const setTheme = (newThemeName: 'dark' | 'light') => {
        setThemeName(newThemeName);
        localStorage.setItem('theme', newThemeName);
    };

    const toggleTheme = () => {
        setTheme(themeName === 'dark' ? 'light' : 'dark');
    };

    // Load theme from localStorage on mount
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme') as 'dark' | 'light';
        if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {
            setThemeName(savedTheme);
        }
    }, []);

    // Apply CSS custom properties for the theme
    useEffect(() => {
        const root = document.documentElement;
        
        // Colors
        root.style.setProperty('--color-primary', theme.colors.primary);
        root.style.setProperty('--color-secondary', theme.colors.secondary);
        root.style.setProperty('--color-accent', theme.colors.accent);
        root.style.setProperty('--color-background', theme.colors.background);
        root.style.setProperty('--color-surface', theme.colors.surface);
        root.style.setProperty('--color-text-primary', theme.colors.text.primary);
        root.style.setProperty('--color-text-secondary', theme.colors.text.secondary);
        root.style.setProperty('--color-text-disabled', theme.colors.text.disabled);
        root.style.setProperty('--color-success', theme.colors.status.success);
        root.style.setProperty('--color-warning', theme.colors.status.warning);
        root.style.setProperty('--color-error', theme.colors.status.error);
        root.style.setProperty('--color-info', theme.colors.status.info);
        root.style.setProperty('--color-border', theme.colors.border);
        root.style.setProperty('--color-shadow', theme.colors.shadow);

        // Typography
        root.style.setProperty('--font-family', theme.typography.fontFamily);
        root.style.setProperty('--font-size-xs', theme.typography.fontSize.xs);
        root.style.setProperty('--font-size-sm', theme.typography.fontSize.sm);
        root.style.setProperty('--font-size-md', theme.typography.fontSize.md);
        root.style.setProperty('--font-size-lg', theme.typography.fontSize.lg);
        root.style.setProperty('--font-size-xl', theme.typography.fontSize.xl);
        root.style.setProperty('--font-size-xxl', theme.typography.fontSize.xxl);

        // Spacing
        root.style.setProperty('--spacing-xs', theme.spacing.xs);
        root.style.setProperty('--spacing-sm', theme.spacing.sm);
        root.style.setProperty('--spacing-md', theme.spacing.md);
        root.style.setProperty('--spacing-lg', theme.spacing.lg);
        root.style.setProperty('--spacing-xl', theme.spacing.xl);
        root.style.setProperty('--spacing-xxl', theme.spacing.xxl);

        // Border radius
        root.style.setProperty('--border-radius-sm', theme.borderRadius.sm);
        root.style.setProperty('--border-radius-md', theme.borderRadius.md);
        root.style.setProperty('--border-radius-lg', theme.borderRadius.lg);
        root.style.setProperty('--border-radius-xl', theme.borderRadius.xl);
        root.style.setProperty('--border-radius-full', theme.borderRadius.full);

        // Animation
        root.style.setProperty('--animation-duration-fast', theme.animation.duration.fast);
        root.style.setProperty('--animation-duration-normal', theme.animation.duration.normal);
        root.style.setProperty('--animation-duration-slow', theme.animation.duration.slow);

        // Set theme class on body
        document.body.className = `theme-${themeName}`;
    }, [theme, themeName]);

    const contextValue: ThemeContextType = {
        theme,
        themeName,
        setTheme,
        toggleTheme
    };

    return (
        <ThemeContext.Provider value={contextValue}>
            {children}
        </ThemeContext.Provider>
    );
};

export const useTheme = (): ThemeContextType => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

export { darkTheme, lightTheme };
export default ThemeContext;
