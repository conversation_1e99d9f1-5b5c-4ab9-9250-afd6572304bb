{"version": 3, "file": "EventManager.d.ts", "sourceRoot": "", "sources": ["../../../client_packages/events/EventManager.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,MAAM,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACrE,MAAM,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAEzG,MAAM,WAAW,aAAa;IAC1B,QAAQ,EAAE,aAAa,CAAC;IACxB,IAAI,EAAE,OAAO,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,qBAAa,YAAY;IACrB,OAAO,CAAC,MAAM,CAA2C;IACzD,OAAO,CAAC,WAAW,CAAyB;IAC5C,OAAO,CAAC,YAAY,CAA6D;IACjF,OAAO,CAAC,cAAc,CAAe;IAErC;;OAEG;IACI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,GAAE,MAAU,GAAG,IAAI;IAIjF;;OAEG;IACI,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,GAAE,MAAU,GAAG,IAAI;IAInF;;OAEG;IACI,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,aAAa,GAAG,IAAI;IAgB7D;;OAEG;IACI,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI;IAUnD;;OAEG;IACU,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAyCnE;;OAEG;IACI,GAAG,CAAC,UAAU,EAAE,eAAe,GAAG,IAAI;IAK7C;;OAEG;IACI,gBAAgB,CAAC,UAAU,EAAE,eAAe,GAAG,IAAI;IAQ1D;;OAEG;IACI,gBAAgB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAKlD;;OAEG;IACI,aAAa,IAAI,MAAM,EAAE;IAIhC;;OAEG;IACI,YAAY,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAI/C;;OAEG;IACI,eAAe,IAAI,KAAK,CAAC;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,GAAG,EAAE,CAAC;QAAC,SAAS,EAAE,IAAI,CAAA;KAAE,CAAC;IAI/E;;OAEG;IACI,iBAAiB,IAAI,IAAI;IAKhC;;OAEG;IACI,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAuBnE;;OAEG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,sBAAsB;IAIxD;;OAEG;IACI,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI;IAUnE;;OAEG;IACI,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAK9C;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAexB;;OAEG;YACW,gBAAgB;IAkB9B;;OAEG;IACH,OAAO,CAAC,YAAY;IAapB;;OAEG;IACI,aAAa,IAAI;QAAE,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE;CAS1D;AAED;;;GAGG;AACH,qBAAa,sBAAsB;IAE3B,OAAO,CAAC,YAAY;IACpB,OAAO,CAAC,MAAM;gBADN,YAAY,EAAE,YAAY,EAC1B,MAAM,EAAE,MAAM;IAGnB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,GAAE,MAAU,GAAG,IAAI;IAI1E,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,GAAE,MAAU,GAAG,IAAI;IAI5E,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,aAAa,GAAG,IAAI;IAIhD,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAI5D,gBAAgB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAI3C,YAAY,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAIxC,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;CAGtE"}