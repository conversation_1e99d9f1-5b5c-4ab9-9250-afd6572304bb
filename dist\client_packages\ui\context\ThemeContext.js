"use strict";
/**
 * Theme Context Provider
 * Provides theme configuration and styling to React components
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.lightTheme = exports.darkTheme = exports.useTheme = exports.ThemeProvider = void 0;
const react_1 = __importStar(require("react"));
const darkTheme = {
    name: 'dark',
    colors: {
        primary: '#3B82F6',
        secondary: '#6366F1',
        accent: '#F59E0B',
        background: '#0F172A',
        surface: '#1E293B',
        text: {
            primary: '#F8FAFC',
            secondary: '#CBD5E1',
            disabled: '#64748B'
        },
        status: {
            success: '#10B981',
            warning: '#F59E0B',
            error: '#EF4444',
            info: '#3B82F6'
        },
        border: '#334155',
        shadow: 'rgba(0, 0, 0, 0.5)'
    },
    typography: {
        fontFamily: '"Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
        fontSize: {
            xs: '0.75rem',
            sm: '0.875rem',
            md: '1rem',
            lg: '1.125rem',
            xl: '1.25rem',
            xxl: '1.5rem'
        },
        fontWeight: {
            light: 300,
            normal: 400,
            medium: 500,
            bold: 700
        }
    },
    spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        xxl: '3rem'
    },
    borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        xl: '0.75rem',
        full: '9999px'
    },
    animation: {
        duration: {
            fast: '150ms',
            normal: '300ms',
            slow: '500ms'
        },
        easing: {
            ease: 'ease',
            easeIn: 'ease-in',
            easeOut: 'ease-out',
            easeInOut: 'ease-in-out'
        }
    }
};
exports.darkTheme = darkTheme;
const lightTheme = {
    ...darkTheme,
    name: 'light',
    colors: {
        ...darkTheme.colors,
        background: '#FFFFFF',
        surface: '#F8FAFC',
        text: {
            primary: '#0F172A',
            secondary: '#475569',
            disabled: '#94A3B8'
        },
        border: '#E2E8F0',
        shadow: 'rgba(0, 0, 0, 0.1)'
    }
};
exports.lightTheme = lightTheme;
const ThemeContext = (0, react_1.createContext)(null);
const ThemeProvider = ({ children }) => {
    const [themeName, setThemeName] = (0, react_1.useState)('dark');
    const theme = themeName === 'dark' ? darkTheme : lightTheme;
    const setTheme = (newThemeName) => {
        setThemeName(newThemeName);
        localStorage.setItem('theme', newThemeName);
    };
    const toggleTheme = () => {
        setTheme(themeName === 'dark' ? 'light' : 'dark');
    };
    // Load theme from localStorage on mount
    (0, react_1.useEffect)(() => {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {
            setThemeName(savedTheme);
        }
    }, []);
    // Apply CSS custom properties for the theme
    (0, react_1.useEffect)(() => {
        const root = document.documentElement;
        // Colors
        root.style.setProperty('--color-primary', theme.colors.primary);
        root.style.setProperty('--color-secondary', theme.colors.secondary);
        root.style.setProperty('--color-accent', theme.colors.accent);
        root.style.setProperty('--color-background', theme.colors.background);
        root.style.setProperty('--color-surface', theme.colors.surface);
        root.style.setProperty('--color-text-primary', theme.colors.text.primary);
        root.style.setProperty('--color-text-secondary', theme.colors.text.secondary);
        root.style.setProperty('--color-text-disabled', theme.colors.text.disabled);
        root.style.setProperty('--color-success', theme.colors.status.success);
        root.style.setProperty('--color-warning', theme.colors.status.warning);
        root.style.setProperty('--color-error', theme.colors.status.error);
        root.style.setProperty('--color-info', theme.colors.status.info);
        root.style.setProperty('--color-border', theme.colors.border);
        root.style.setProperty('--color-shadow', theme.colors.shadow);
        // Typography
        root.style.setProperty('--font-family', theme.typography.fontFamily);
        root.style.setProperty('--font-size-xs', theme.typography.fontSize.xs);
        root.style.setProperty('--font-size-sm', theme.typography.fontSize.sm);
        root.style.setProperty('--font-size-md', theme.typography.fontSize.md);
        root.style.setProperty('--font-size-lg', theme.typography.fontSize.lg);
        root.style.setProperty('--font-size-xl', theme.typography.fontSize.xl);
        root.style.setProperty('--font-size-xxl', theme.typography.fontSize.xxl);
        // Spacing
        root.style.setProperty('--spacing-xs', theme.spacing.xs);
        root.style.setProperty('--spacing-sm', theme.spacing.sm);
        root.style.setProperty('--spacing-md', theme.spacing.md);
        root.style.setProperty('--spacing-lg', theme.spacing.lg);
        root.style.setProperty('--spacing-xl', theme.spacing.xl);
        root.style.setProperty('--spacing-xxl', theme.spacing.xxl);
        // Border radius
        root.style.setProperty('--border-radius-sm', theme.borderRadius.sm);
        root.style.setProperty('--border-radius-md', theme.borderRadius.md);
        root.style.setProperty('--border-radius-lg', theme.borderRadius.lg);
        root.style.setProperty('--border-radius-xl', theme.borderRadius.xl);
        root.style.setProperty('--border-radius-full', theme.borderRadius.full);
        // Animation
        root.style.setProperty('--animation-duration-fast', theme.animation.duration.fast);
        root.style.setProperty('--animation-duration-normal', theme.animation.duration.normal);
        root.style.setProperty('--animation-duration-slow', theme.animation.duration.slow);
        // Set theme class on body
        document.body.className = `theme-${themeName}`;
    }, [theme, themeName]);
    const contextValue = {
        theme,
        themeName,
        setTheme,
        toggleTheme
    };
    return (<ThemeContext.Provider value={contextValue}>
            {children}
        </ThemeContext.Provider>);
};
exports.ThemeProvider = ThemeProvider;
const useTheme = () => {
    const context = (0, react_1.useContext)(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};
exports.useTheme = useTheme;
exports.default = ThemeContext;
//# sourceMappingURL=ThemeContext.js.map