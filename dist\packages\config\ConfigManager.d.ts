/**
 * Configuration Manager
 * Handles all server configuration settings
 */
export interface ServerConfig {
    server: {
        name: string;
        maxPlayers: number;
        port: number;
        gamemode: string;
        streamDistance: number;
        announce: boolean;
    };
    database: {
        host: string;
        port: number;
        username: string;
        password: string;
        database: string;
        connectionLimit: number;
    };
    security: {
        bcryptRounds: number;
        sessionTimeout: number;
        maxLoginAttempts: number;
        lockoutDuration: number;
    };
    gameplay: {
        startingMoney: number;
        startingPosition: {
            x: number;
            y: number;
            z: number;
        };
        respawnTime: number;
        maxCharacters: number;
    };
    ui: {
        theme: string;
        language: string;
        animations: boolean;
    };
}
export declare class ConfigManager {
    private config;
    private configPath;
    constructor();
    /**
     * Load configuration from file
     */
    load(): Promise<void>;
    /**
     * Save configuration to file
     */
    save(): Promise<void>;
    /**
     * Set default configuration values
     */
    private setDefaultConfig;
    /**
     * Get configuration value by path
     */
    get<T>(path: string): T;
    /**
     * Set configuration value by path
     */
    set(path: string, value: any): void;
    /**
     * Get full configuration
     */
    getAll(): ServerConfig;
    /**
     * Update configuration with partial data
     */
    update(partialConfig: Partial<ServerConfig>): void;
    /**
     * Reset to default configuration
     */
    reset(): void;
    /**
     * Validate configuration
     */
    validate(): boolean;
}
//# sourceMappingURL=ConfigManager.d.ts.map