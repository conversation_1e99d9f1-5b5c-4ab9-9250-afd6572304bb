"use strict";
/**
 * Main React Application Component
 * Root component for the entire UI system
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.App = void 0;
const react_1 = __importStar(require("react"));
const UIContext_1 = require("./context/UIContext");
const ThemeContext_1 = require("./context/ThemeContext");
const NotificationSystem_1 = require("./components/NotificationSystem");
const HUD_1 = require("./components/HUD/HUD");
const AuthenticationModal_1 = require("./components/Authentication/AuthenticationModal");
const CharacterSelection_1 = require("./components/Character/CharacterSelection");
const LoadingScreen_1 = require("./components/LoadingScreen");
const DebugPanel_1 = require("./components/Debug/DebugPanel");
require("./styles/global.css");
const App = () => {
    const [appState, setAppState] = (0, react_1.useState)({
        isLoading: true,
        isAuthenticated: false,
        hasCharacter: false,
        showDebug: false,
        currentScreen: 'loading'
    });
    (0, react_1.useEffect)(() => {
        // Initialize app
        initializeApp();
        // Listen for RAGE MP events
        if (typeof mp !== 'undefined') {
            // Authentication events
            mp.events.add('ui:showLogin', () => {
                setAppState(prev => ({ ...prev, currentScreen: 'auth', isLoading: false }));
            });
            mp.events.add('ui:showCharacterSelection', () => {
                setAppState(prev => ({
                    ...prev,
                    currentScreen: 'character-selection',
                    isAuthenticated: true
                }));
            });
            mp.events.add('ui:enterGame', () => {
                setAppState(prev => ({
                    ...prev,
                    currentScreen: 'game',
                    hasCharacter: true
                }));
            });
            mp.events.add('ui:toggleDebug', () => {
                setAppState(prev => ({ ...prev, showDebug: !prev.showDebug }));
            });
        }
        return () => {
            // Cleanup event listeners
            if (typeof mp !== 'undefined') {
                mp.events.remove('ui:showLogin');
                mp.events.remove('ui:showCharacterSelection');
                mp.events.remove('ui:enterGame');
                mp.events.remove('ui:toggleDebug');
            }
        };
    }, []);
    const initializeApp = async () => {
        try {
            // Simulate initialization delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Check if player needs authentication
            setAppState(prev => ({
                ...prev,
                isLoading: false,
                currentScreen: 'auth'
            }));
        }
        catch (error) {
            console.error('Failed to initialize app:', error);
        }
    };
    const renderCurrentScreen = () => {
        switch (appState.currentScreen) {
            case 'loading':
                return <LoadingScreen_1.LoadingScreen />;
            case 'auth':
                return (<AuthenticationModal_1.AuthenticationModal isOpen={true} onAuthenticated={() => {
                        setAppState(prev => ({
                            ...prev,
                            isAuthenticated: true,
                            currentScreen: 'character-selection'
                        }));
                    }}/>);
            case 'character-selection':
                return (<CharacterSelection_1.CharacterSelection onCharacterSelected={() => {
                        setAppState(prev => ({
                            ...prev,
                            hasCharacter: true,
                            currentScreen: 'game'
                        }));
                    }}/>);
            case 'game':
                return <HUD_1.HUD />;
            default:
                return <LoadingScreen_1.LoadingScreen />;
        }
    };
    return (<ThemeContext_1.ThemeProvider>
            <UIContext_1.UIProvider>
                <div className="app-container">
                    {/* Main UI Content */}
                    {renderCurrentScreen()}
                    
                    {/* Always visible components */}
                    <NotificationSystem_1.NotificationSystem />
                    
                    {/* Debug panel (only in development or when enabled) */}
                    {appState.showDebug && <DebugPanel_1.DebugPanel />}
                    
                    {/* Global overlays */}
                    <div id="modal-root"/>
                    <div id="tooltip-root"/>
                </div>
            </UIContext_1.UIProvider>
        </ThemeContext_1.ThemeProvider>);
};
exports.App = App;
exports.default = exports.App;
//# sourceMappingURL=App.js.map