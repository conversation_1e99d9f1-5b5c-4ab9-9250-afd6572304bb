{"version": 3, "file": "VehicleManager.js", "sourceRoot": "", "sources": ["../../../packages/managers/VehicleManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AACzC,iEAA8D;AAC9D,yDAAsD;AAkCtD,MAAa,cAAc;IAMvB;QALQ,aAAQ,GAAiC,IAAI,GAAG,EAAE,CAAC;QAGnD,uBAAkB,GAA0B,IAAI,CAAC;QAGrD,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACvC,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,eAAgC,EAAE,YAA0B;QAC/E,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CACtB,KAAa,EACb,QAAmB,EACnB,UAAkB,CAAC,EACnB,OAAgB,EAChB,YAAoB,CAAC;QAErB,IAAI,CAAC;YACD,+BAA+B;YAC/B,MAAM,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE;gBACvD,OAAO;gBACP,SAAS;gBACT,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBACvC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,eAAM,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;gBACvD,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,wBAAwB;YACxB,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAEpD,sBAAsB;YACtB,MAAM,WAAW,GAAyB;gBACtC,KAAK;gBACL,QAAQ,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE;gBACzD,OAAO;gBACP,SAAS;gBACT,MAAM,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;gBAClC,MAAM,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;gBAClC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,IAAI;gBAChB,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,CAAC;gBACV,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,KAAK;aACrB,CAAC;YAEF,IAAI,OAAO,EAAE,CAAC;gBACV,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;YAClC,CAAC;YAED,yCAAyC;YACzC,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC7C;;;;oFAIgE,EAChE;oBACI,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS;oBACtE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC;oBACtE,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,UAAU;oBACrE,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,QAAQ;oBAC/E,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,WAAW,CAAC,WAAW;iBACrE,CACJ,CAAC;gBAEF,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBAC5B,WAAW,CAAC,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;gBACrC,CAAC;YACL,CAAC;YAED,eAAe,CAAC,WAAW,GAAG,WAA0B,CAAC;YACzD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;YAE/C,eAAM,CAAC,IAAI,CAAC,uBAAuB,KAAK,SAAS,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;YAChE,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;YAEjE,OAAO,eAAe,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,wBAAwB;QACjC,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAEpD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAClD,mDAAmD,CACtD,CAAC;YAEF,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3C,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,OAAO;YACX,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;YAED,eAAM,CAAC,OAAO,CAAC,YAAY,WAAW,CAAC,MAAM,yBAAyB,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,IAAS;QACxC,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAEnF,MAAM,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE;gBAC5D,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,YAAY;gBAC9B,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,SAAS;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,eAAM,CAAC,KAAK,CAAC,0CAA0C,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;gBACrE,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACpD,eAAe,CAAC,WAAW,GAAG;gBAC1B,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE;gBACxE,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC/B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC/B,WAAW,EAAE,IAAI,CAAC,YAAY;gBAC9B,YAAY,EAAE,IAAI,CAAC,aAAa;gBAChC,UAAU,EAAE,IAAI,CAAC,WAAW;gBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;gBACrD,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;gBACxC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;gBAC9C,WAAW,EAAE,IAAI,CAAC,YAAY;gBAC9B,aAAa,EAAE,IAAI,CAAC,cAAc;aACrC,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;YAC/C,OAAO,eAAe,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,SAAiB;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAAe;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAC5C,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,KAAK,OAAO,CACtD,CAAC;IACN,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,OAAwB;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAAE,OAAO;QAE5D,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC9B;;;;8BAIc,EACd;gBACI,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO;gBAC3E,OAAO,CAAC,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,UAAU;gBAChE,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,OAAO;gBACrD,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,QAAQ;gBACxD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC;gBACjD,OAAO,CAAC,WAAW,CAAC,EAAE;aACzB,CACJ,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,uBAAuB,OAAO,CAAC,WAAW,CAAC,KAAK,KAAK,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,0BAA0B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe;QACxB,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEzC,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CACrD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CACpC,eAAM,CAAC,KAAK,CAAC,0BAA0B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAC/D,CACJ,CAAC;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAChC,eAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CAAC,OAAwB;QAC/C,IAAI,CAAC;YACD,uBAAuB;YACvB,IAAI,OAAO,CAAC,WAAW,EAAE,EAAE,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC9B,mCAAmC,EACnC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAC3B,CAAC;YACN,CAAC;YAED,yBAAyB;YACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,CAAC,OAAO,EAAE,CAAC;YAElB,eAAM,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC;YAClF,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,mBAAmB;QACvB,MAAM,KAAK,GAAG,sCAAsC,CAAC;QACrD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAAkB;QACpC,MAAM,eAAe,GAAG,OAA0B,CAAC;QAEnD,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnF,eAAe,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5C,eAAe,CAAC,WAAW,GAAG,KAAK,CAAC;QACpC,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC;QAEhD,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,KAAa;QAC1C,0DAA0D;QAC1D,4DAA4D;QAC5D,OAAO,GAAG,CAAC,CAAC,wBAAwB;IACxC,CAAC;IAED;;OAEG;IACK,eAAe;QACnB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,sBAAsB;QAEjC,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,iBAAiB;QACrB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC3C,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxC,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,UAAU;gBACpF,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,GAAG,QAAQ,CAAC;gBAE5D,OAAO,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC;gBAChF,OAAO,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;gBAEpC,iCAAiC;gBACjC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBAClD,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;oBACvB,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,KAAK,CAAC;oBAErC,mBAAmB;oBACnB,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;oBACtC,IAAI,MAAM,EAAE,CAAC;wBACT,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,EAAE,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC,CAAC;oBACzF,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;CACJ;AAhXD,wCAgXC"}