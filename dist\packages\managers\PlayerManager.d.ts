/**
 * Player Manager
 * Handles all player-related operations and data management
 */
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';
export interface PlayerData {
    id: number;
    username: string;
    email: string;
    socialClub: string;
    serial: string;
    ipAddress: string;
    registrationDate: Date;
    lastLogin: Date | null;
    loginAttempts: number;
    lockedUntil: Date | null;
    isBanned: boolean;
    banReason: string | null;
    adminLevel: number;
    totalPlaytime: number;
}
export interface CharacterData {
    id: number;
    userId: number;
    name: string;
    age: number;
    gender: 'male' | 'female';
    money: number;
    bankMoney: number;
    position: {
        x: number;
        y: number;
        z: number;
    };
    heading: number;
    dimension: number;
    interior: number;
    health: number;
    armor: number;
    hunger: number;
    thirst: number;
    stress: number;
    job: string;
    jobRank: number;
    jobSalary: number;
    faction: string | null;
    factionRank: number;
    skinData: any;
    clothesData: any;
    isActive: boolean;
    playtime: number;
}
export interface ExtendedPlayer extends PlayerMp {
    userData?: PlayerData;
    characterData?: CharacterData;
    isLoggedIn: boolean;
    isCharacterSelected: boolean;
    loginAttempts: number;
    lastActivity: Date;
    sessionStartTime: Date;
}
export declare class PlayerManager {
    private players;
    private databaseManager;
    private eventManager;
    constructor();
    /**
     * Set dependencies (called by ServerCore)
     */
    setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void;
    /**
     * Handle player join
     */
    onPlayerJoin(player: PlayerMp): Promise<void>;
    /**
     * Handle player quit
     */
    onPlayerQuit(player: PlayerMp): Promise<void>;
    /**
     * Get extended player by ID
     */
    getPlayer(playerId: number): ExtendedPlayer | undefined;
    /**
     * Get extended player by name
     */
    getPlayerByName(name: string): ExtendedPlayer | undefined;
    /**
     * Get all online players
     */
    getAllPlayers(): ExtendedPlayer[];
    /**
     * Get logged in players
     */
    getLoggedInPlayers(): ExtendedPlayer[];
    /**
     * Get players with character selected
     */
    getPlayersInGame(): ExtendedPlayer[];
    /**
     * Save all player data
     */
    saveAllPlayers(): Promise<void>;
    /**
     * Load player data from database
     */
    private loadPlayerData;
    /**
     * Save player data to database
     */
    private savePlayerData;
    /**
     * Update player playtime
     */
    private updatePlaytime;
    /**
     * Extend player object with additional properties
     */
    private extendPlayer;
    /**
     * Set initial spawn position
     */
    private setInitialSpawn;
    /**
     * Kick player with reason
     */
    kickPlayer(player: ExtendedPlayer, reason?: string): void;
    /**
     * Ban player with reason
     */
    banPlayer(player: ExtendedPlayer, reason?: string, adminName?: string): Promise<void>;
    /**
     * Send notification to player
     */
    notify(player: ExtendedPlayer, message: string, type?: string): void;
    /**
     * Send notification to all players
     */
    notifyAll(message: string, type?: string): void;
    /**
     * Get player count
     */
    getPlayerCount(): number;
    /**
     * Get online player count
     */
    getOnlinePlayerCount(): number;
}
//# sourceMappingURL=PlayerManager.d.ts.map