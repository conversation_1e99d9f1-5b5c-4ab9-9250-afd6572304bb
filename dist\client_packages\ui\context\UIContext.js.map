{"version": 3, "file": "UIContext.js", "sourceRoot": "", "sources": ["../../../../client_packages/ui/context/UIContext.tsx"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAA2F;AA4C3F,MAAM,YAAY,GAAY;IAC1B,SAAS,EAAE,IAAI;IACf,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,EAAE;IACjB,MAAM,EAAE,EAAE;IACV,UAAU,EAAE,IAAI;IAChB,WAAW,EAAE,IAAI;IACjB,aAAa,EAAE,KAAK;IACpB,aAAa,EAAE,IAAI;IACnB,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF,MAAM,SAAS,GAAG,CAAC,KAAc,EAAE,MAAgB,EAAW,EAAE;IAC5D,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,aAAa;YACd,OAAO,EAAE,GAAG,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QAEnD,KAAK,oBAAoB;YACrB,OAAO,EAAE,GAAG,KAAK,EAAE,aAAa,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QAEvD,KAAK,kBAAkB;YACnB,OAAO;gBACH,GAAG,KAAK;gBACR,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC;aAC1D,CAAC;QAEN,KAAK,qBAAqB;YACtB,OAAO;gBACH,GAAG,KAAK;gBACR,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;aAC1E,CAAC;QAEN,KAAK,qBAAqB;YACtB,OAAO,EAAE,GAAG,KAAK,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC;QAE3C,KAAK,YAAY;YACb,OAAO;gBACH,GAAG,KAAK;gBACR,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;oBACzC,CAAC,CAAC,KAAK,CAAC,MAAM;oBACd,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;aAC1C,CAAC;QAEN,KAAK,aAAa;YACd,OAAO;gBACH,GAAG,KAAK;gBACR,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC;aACzD,CAAC;QAEN,KAAK,iBAAiB;YAClB,OAAO,EAAE,GAAG,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QAEpD,KAAK,kBAAkB;YACnB,OAAO,EAAE,GAAG,KAAK,EAAE,WAAW,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QAErD,KAAK,oBAAoB;YACrB,OAAO,EAAE,GAAG,KAAK,EAAE,aAAa,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QAEvD,KAAK,oBAAoB;YACrB,OAAO,EAAE,GAAG,KAAK,EAAE,aAAa,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QAEvD,KAAK,eAAe;YAChB,OAAO,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;QAElD;YACI,OAAO,KAAK,CAAC;IACrB,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,SAAS,GAAG,IAAA,qBAAa,EAGrB,IAAI,CAAC,CAAC;AAET,MAAM,UAAU,GAAsC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC1E,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,kBAAU,EAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IAE9D,MAAM,OAAO,GAAc;QACvB,UAAU,EAAE,CAAC,OAAgB,EAAE,EAAE,CAC7B,QAAQ,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;QAEvD,gBAAgB,EAAE,CAAC,MAAgC,EAAE,EAAE,CACnD,QAAQ,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;QAE7D,eAAe,EAAE,CAAC,YAA8B,EAAE,EAAE,CAChD,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;QAEjE,kBAAkB,EAAE,CAAC,EAAU,EAAE,EAAE,CAC/B,QAAQ,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QAE1D,kBAAkB,EAAE,GAAG,EAAE,CACrB,QAAQ,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAAC;QAE7C,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAC7B,QAAQ,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAExD,UAAU,EAAE,CAAC,SAAiB,EAAE,EAAE,CAC9B,QAAQ,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;QAEzD,aAAa,EAAE,CAAC,OAAgB,EAAE,EAAE,CAChC,QAAQ,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;QAE3D,cAAc,EAAE,CAAC,OAAgB,EAAE,EAAE,CACjC,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;QAE5D,gBAAgB,EAAE,CAAC,OAAgB,EAAE,EAAE,CACnC,QAAQ,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;QAE9D,gBAAgB,EAAE,CAAC,IAAS,EAAE,EAAE,CAC5B,QAAQ,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAE3D,WAAW,EAAE,CAAC,IAAS,EAAE,EAAE,CACvB,QAAQ,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;KACzD,CAAC;IAEF,qCAAqC;IACrC,IAAA,iBAAS,EAAC,GAAG,EAAE;QACX,MAAM,aAAa,GAAG,CAAC,KAAkB,EAAE,EAAE;YACzC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;YAE/B,QAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,qBAAqB;oBACtB,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBAChC,MAAM;gBAEV,KAAK,wBAAwB;oBACzB,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACtC,MAAM;gBAEV,KAAK,uBAAuB;oBACxB,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC7B,MAAM;gBAEV,KAAK,mBAAmB;oBACpB,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBACjC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC/B,MAAM;gBAEV,KAAK,sBAAsB;oBACvB,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBACjC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC/B,MAAM;gBAEV,KAAK,4BAA4B;oBAC7B,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;oBAChD,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC/B,MAAM;gBAEV,KAAK,qBAAqB;oBACtB,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;oBAC/C,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBACjC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAChC,MAAM;gBAEV,KAAK,eAAe;oBAChB,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACtC,MAAM;gBAEV,KAAK,gBAAgB;oBACjB,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACvC,MAAM;gBAEV,KAAK,sBAAsB;oBACvB,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACzC,MAAM;gBAEV,KAAK,eAAe;oBAChB,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBACpC,MAAM;gBAEV,KAAK,gBAAgB;oBACjB,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBACrC,MAAM;YACd,CAAC;QACL,CAAC,CAAC;QAEF,wCAAwC;QACxC,MAAM,UAAU,GAAG;YACf,qBAAqB;YACrB,wBAAwB;YACxB,uBAAuB;YACvB,mBAAmB;YACnB,sBAAsB;YACtB,4BAA4B;YAC5B,qBAAqB;YACrB,eAAe;YACf,gBAAgB;YAChB,sBAAsB;YACtB,eAAe;YACf,gBAAgB;SACnB,CAAC;QAEF,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC3B,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAA8B,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE;YACR,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC3B,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAA8B,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CACH,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAC1C;YAAA,CAAC,QAAQ,CACb;QAAA,EAAE,SAAS,CAAC,QAAQ,CAAC,CACxB,CAAC;AACN,CAAC,CAAC;AAtIW,QAAA,UAAU,cAsIrB;AAEK,MAAM,KAAK,GAAG,GAAG,EAAE;IACtB,MAAM,OAAO,GAAG,IAAA,kBAAU,EAAC,SAAS,CAAC,CAAC;IACtC,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AANW,QAAA,KAAK,SAMhB;AAEF,kBAAe,SAAS,CAAC"}