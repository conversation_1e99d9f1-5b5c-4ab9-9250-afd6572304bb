/**
 * UI Context Provider
 * Provides UI state and actions to React components
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { NotificationData } from '../UIManager';

export interface UIState {
    isLoading: boolean;
    currentScreen: 'loading' | 'auth' | 'character-selection' | 'game';
    notifications: NotificationData[];
    modals: string[];
    hudVisible: boolean;
    chatVisible: boolean;
    cursorVisible: boolean;
    characterData: any;
    userData: any;
}

export interface UIActions {
    setLoading: (loading: boolean) => void;
    setCurrentScreen: (screen: UIState['currentScreen']) => void;
    addNotification: (notification: NotificationData) => void;
    removeNotification: (id: string) => void;
    clearNotifications: () => void;
    openModal: (modalName: string) => void;
    closeModal: (modalName: string) => void;
    setHUDVisible: (visible: boolean) => void;
    setChatVisible: (visible: boolean) => void;
    setCursorVisible: (visible: boolean) => void;
    setCharacterData: (data: any) => void;
    setUserData: (data: any) => void;
}

type UIAction =
    | { type: 'SET_LOADING'; payload: boolean }
    | { type: 'SET_CURRENT_SCREEN'; payload: UIState['currentScreen'] }
    | { type: 'ADD_NOTIFICATION'; payload: NotificationData }
    | { type: 'REMOVE_NOTIFICATION'; payload: string }
    | { type: 'CLEAR_NOTIFICATIONS' }
    | { type: 'OPEN_MODAL'; payload: string }
    | { type: 'CLOSE_MODAL'; payload: string }
    | { type: 'SET_HUD_VISIBLE'; payload: boolean }
    | { type: 'SET_CHAT_VISIBLE'; payload: boolean }
    | { type: 'SET_CURSOR_VISIBLE'; payload: boolean }
    | { type: 'SET_CHARACTER_DATA'; payload: any }
    | { type: 'SET_USER_DATA'; payload: any };

const initialState: UIState = {
    isLoading: true,
    currentScreen: 'loading',
    notifications: [],
    modals: [],
    hudVisible: true,
    chatVisible: true,
    cursorVisible: false,
    characterData: null,
    userData: null
};

const uiReducer = (state: UIState, action: UIAction): UIState => {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, isLoading: action.payload };
        
        case 'SET_CURRENT_SCREEN':
            return { ...state, currentScreen: action.payload };
        
        case 'ADD_NOTIFICATION':
            return { 
                ...state, 
                notifications: [...state.notifications, action.payload] 
            };
        
        case 'REMOVE_NOTIFICATION':
            return { 
                ...state, 
                notifications: state.notifications.filter(n => n.id !== action.payload) 
            };
        
        case 'CLEAR_NOTIFICATIONS':
            return { ...state, notifications: [] };
        
        case 'OPEN_MODAL':
            return { 
                ...state, 
                modals: state.modals.includes(action.payload) 
                    ? state.modals 
                    : [...state.modals, action.payload] 
            };
        
        case 'CLOSE_MODAL':
            return { 
                ...state, 
                modals: state.modals.filter(m => m !== action.payload) 
            };
        
        case 'SET_HUD_VISIBLE':
            return { ...state, hudVisible: action.payload };
        
        case 'SET_CHAT_VISIBLE':
            return { ...state, chatVisible: action.payload };
        
        case 'SET_CURSOR_VISIBLE':
            return { ...state, cursorVisible: action.payload };
        
        case 'SET_CHARACTER_DATA':
            return { ...state, characterData: action.payload };
        
        case 'SET_USER_DATA':
            return { ...state, userData: action.payload };
        
        default:
            return state;
    }
};

const UIContext = createContext<{
    state: UIState;
    actions: UIActions;
} | null>(null);

export const UIProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [state, dispatch] = useReducer(uiReducer, initialState);

    const actions: UIActions = {
        setLoading: (loading: boolean) => 
            dispatch({ type: 'SET_LOADING', payload: loading }),
        
        setCurrentScreen: (screen: UIState['currentScreen']) => 
            dispatch({ type: 'SET_CURRENT_SCREEN', payload: screen }),
        
        addNotification: (notification: NotificationData) => 
            dispatch({ type: 'ADD_NOTIFICATION', payload: notification }),
        
        removeNotification: (id: string) => 
            dispatch({ type: 'REMOVE_NOTIFICATION', payload: id }),
        
        clearNotifications: () => 
            dispatch({ type: 'CLEAR_NOTIFICATIONS' }),
        
        openModal: (modalName: string) => 
            dispatch({ type: 'OPEN_MODAL', payload: modalName }),
        
        closeModal: (modalName: string) => 
            dispatch({ type: 'CLOSE_MODAL', payload: modalName }),
        
        setHUDVisible: (visible: boolean) => 
            dispatch({ type: 'SET_HUD_VISIBLE', payload: visible }),
        
        setChatVisible: (visible: boolean) => 
            dispatch({ type: 'SET_CHAT_VISIBLE', payload: visible }),
        
        setCursorVisible: (visible: boolean) => 
            dispatch({ type: 'SET_CURSOR_VISIBLE', payload: visible }),
        
        setCharacterData: (data: any) => 
            dispatch({ type: 'SET_CHARACTER_DATA', payload: data }),
        
        setUserData: (data: any) => 
            dispatch({ type: 'SET_USER_DATA', payload: data })
    };

    // Listen for UI events from the game
    useEffect(() => {
        const handleUIEvent = (event: CustomEvent) => {
            const { type, detail } = event;
            
            switch (type) {
                case 'ui:notification:add':
                    actions.addNotification(detail);
                    break;
                
                case 'ui:notification:remove':
                    actions.removeNotification(detail.id);
                    break;
                
                case 'ui:notification:clear':
                    actions.clearNotifications();
                    break;
                
                case 'ui:auth:showLogin':
                    actions.setCurrentScreen('auth');
                    actions.setCursorVisible(true);
                    break;
                
                case 'ui:auth:showRegister':
                    actions.setCurrentScreen('auth');
                    actions.setCursorVisible(true);
                    break;
                
                case 'ui:character:showSelection':
                    actions.setCurrentScreen('character-selection');
                    actions.setCursorVisible(true);
                    break;
                
                case 'ui:character:loaded':
                    actions.setCharacterData(detail.characterData);
                    actions.setCurrentScreen('game');
                    actions.setCursorVisible(false);
                    break;
                
                case 'ui:hud:toggle':
                    actions.setHUDVisible(detail.visible);
                    break;
                
                case 'ui:chat:toggle':
                    actions.setChatVisible(detail.visible);
                    break;
                
                case 'ui:cursor:setVisible':
                    actions.setCursorVisible(detail.visible);
                    break;
                
                case 'ui:modal:open':
                    actions.openModal(detail.modalName);
                    break;
                
                case 'ui:modal:close':
                    actions.closeModal(detail.modalName);
                    break;
            }
        };

        // Add event listeners for all UI events
        const eventTypes = [
            'ui:notification:add',
            'ui:notification:remove',
            'ui:notification:clear',
            'ui:auth:showLogin',
            'ui:auth:showRegister',
            'ui:character:showSelection',
            'ui:character:loaded',
            'ui:hud:toggle',
            'ui:chat:toggle',
            'ui:cursor:setVisible',
            'ui:modal:open',
            'ui:modal:close'
        ];

        eventTypes.forEach(eventType => {
            window.addEventListener(eventType, handleUIEvent as EventListener);
        });

        return () => {
            eventTypes.forEach(eventType => {
                window.removeEventListener(eventType, handleUIEvent as EventListener);
            });
        };
    }, []);

    return (
        <UIContext.Provider value={{ state, actions }}>
            {children}
        </UIContext.Provider>
    );
};

export const useUI = () => {
    const context = useContext(UIContext);
    if (!context) {
        throw new Error('useUI must be used within a UIProvider');
    }
    return context;
};

export default UIContext;
