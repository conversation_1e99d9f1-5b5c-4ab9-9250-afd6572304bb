"use strict";
/**
 * Player Manager
 * Handles all player-related operations and data management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerManager = void 0;
const Logger_1 = require("../utils/Logger");
const DatabaseManager_1 = require("../database/DatabaseManager");
const EventManager_1 = require("../events/EventManager");
class PlayerManager {
    constructor() {
        this.players = new Map();
        // These will be injected by ServerCore
        this.databaseManager = new DatabaseManager_1.DatabaseManager();
        this.eventManager = new EventManager_1.EventManager();
    }
    /**
     * Set dependencies (called by ServerCore)
     */
    setDependencies(databaseManager, eventManager) {
        this.databaseManager = databaseManager;
        this.eventManager = eventManager;
    }
    /**
     * Handle player join
     */
    async onPlayerJoin(player) {
        try {
            const extendedPlayer = this.extendPlayer(player);
            this.players.set(player.id, extendedPlayer);
            Logger_1.Logger.info(`👤 Player ${player.name} (${player.socialClub}) joined from ${player.ip}`);
            // Load player data from database
            await this.loadPlayerData(extendedPlayer);
            // Emit player join event
            await this.eventManager.emit('player:join', extendedPlayer);
            // Set initial spawn position
            this.setInitialSpawn(extendedPlayer);
        }
        catch (error) {
            Logger_1.Logger.error(`Error handling player join for ${player.name}:`, error);
        }
    }
    /**
     * Handle player quit
     */
    async onPlayerQuit(player) {
        try {
            const extendedPlayer = this.players.get(player.id);
            if (!extendedPlayer)
                return;
            Logger_1.Logger.info(`👋 Player ${player.name} left the server`);
            // Save player data
            if (extendedPlayer.isLoggedIn) {
                await this.savePlayerData(extendedPlayer);
            }
            // Update playtime
            await this.updatePlaytime(extendedPlayer);
            // Emit player quit event
            await this.eventManager.emit('player:quit', extendedPlayer);
            // Remove from active players
            this.players.delete(player.id);
        }
        catch (error) {
            Logger_1.Logger.error(`Error handling player quit for ${player.name}:`, error);
        }
    }
    /**
     * Get extended player by ID
     */
    getPlayer(playerId) {
        return this.players.get(playerId);
    }
    /**
     * Get extended player by name
     */
    getPlayerByName(name) {
        for (const player of this.players.values()) {
            if (player.name.toLowerCase() === name.toLowerCase()) {
                return player;
            }
        }
        return undefined;
    }
    /**
     * Get all online players
     */
    getAllPlayers() {
        return Array.from(this.players.values());
    }
    /**
     * Get logged in players
     */
    getLoggedInPlayers() {
        return this.getAllPlayers().filter(player => player.isLoggedIn);
    }
    /**
     * Get players with character selected
     */
    getPlayersInGame() {
        return this.getAllPlayers().filter(player => player.isCharacterSelected);
    }
    /**
     * Save all player data
     */
    async saveAllPlayers() {
        Logger_1.Logger.info('💾 Saving all player data...');
        const savePromises = this.getLoggedInPlayers().map(player => this.savePlayerData(player).catch(error => Logger_1.Logger.error(`Failed to save data for ${player.name}:`, error)));
        await Promise.all(savePromises);
        Logger_1.Logger.success('✅ All player data saved');
    }
    /**
     * Load player data from database
     */
    async loadPlayerData(player) {
        try {
            const userData = await this.databaseManager.execute('SELECT * FROM users WHERE social_club = ? OR serial = ?', [player.socialClub, player.serial]);
            if (userData && userData.length > 0) {
                player.userData = userData[0];
                Logger_1.Logger.debug(`Loaded user data for ${player.name}`);
            }
            else {
                Logger_1.Logger.debug(`No user data found for ${player.name}`);
            }
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to load player data for ${player.name}:`, error);
        }
    }
    /**
     * Save player data to database
     */
    async savePlayerData(player) {
        if (!player.userData || !player.characterData)
            return;
        try {
            // Save user data
            await this.databaseManager.execute(`UPDATE users SET 
                 last_login = NOW(), 
                 total_playtime = ?,
                 ip_address = ?
                 WHERE id = ?`, [player.userData.totalPlaytime, player.ip, player.userData.id]);
            // Save character data
            if (player.characterData) {
                await this.databaseManager.execute(`UPDATE characters SET 
                     money = ?, bank_money = ?, 
                     position_x = ?, position_y = ?, position_z = ?, heading = ?,
                     dimension = ?, interior = ?,
                     health = ?, armor = ?,
                     hunger = ?, thirst = ?, stress = ?,
                     playtime = ?
                     WHERE id = ?`, [
                    player.characterData.money, player.characterData.bankMoney,
                    player.position.x, player.position.y, player.position.z, player.heading,
                    player.dimension, player.characterData.interior,
                    player.health, player.armour,
                    player.characterData.hunger, player.characterData.thirst, player.characterData.stress,
                    player.characterData.playtime,
                    player.characterData.id
                ]);
            }
            Logger_1.Logger.debug(`Saved player data for ${player.name}`);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to save player data for ${player.name}:`, error);
        }
    }
    /**
     * Update player playtime
     */
    async updatePlaytime(player) {
        if (!player.sessionStartTime)
            return;
        const sessionTime = Date.now() - player.sessionStartTime.getTime();
        const sessionMinutes = Math.floor(sessionTime / 60000);
        if (player.userData) {
            player.userData.totalPlaytime += sessionMinutes;
        }
        if (player.characterData) {
            player.characterData.playtime += sessionMinutes;
        }
        Logger_1.Logger.debug(`Updated playtime for ${player.name}: +${sessionMinutes} minutes`);
    }
    /**
     * Extend player object with additional properties
     */
    extendPlayer(player) {
        const extendedPlayer = player;
        extendedPlayer.isLoggedIn = false;
        extendedPlayer.isCharacterSelected = false;
        extendedPlayer.loginAttempts = 0;
        extendedPlayer.lastActivity = new Date();
        extendedPlayer.sessionStartTime = new Date();
        return extendedPlayer;
    }
    /**
     * Set initial spawn position
     */
    setInitialSpawn(player) {
        // Spawn at default location initially
        const spawnPos = new mp.Vector3(-1037.8, -2738.5, 20.2);
        player.spawn(spawnPos, 0);
        player.dimension = 0;
        // Freeze player until authentication
        // player.freezePosition(true); // This method might not exist in RAGE MP
    }
    /**
     * Kick player with reason
     */
    kickPlayer(player, reason = 'No reason specified') {
        Logger_1.Logger.info(`🦵 Kicking player ${player.name}: ${reason}`);
        player.kick(reason);
    }
    /**
     * Ban player with reason
     */
    async banPlayer(player, reason = 'No reason specified', adminName = 'System') {
        try {
            Logger_1.Logger.info(`🔨 Banning player ${player.name}: ${reason}`);
            // Update database
            if (player.userData) {
                await this.databaseManager.execute('UPDATE users SET is_banned = TRUE, ban_reason = ? WHERE id = ?', [reason, player.userData.id]);
            }
            // Emit ban event
            await this.eventManager.emit('player:banned', player, reason, adminName);
            // Kick player
            player.ban(reason);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to ban player ${player.name}:`, error);
        }
    }
    /**
     * Send notification to player
     */
    notify(player, message, type = 'info') {
        player.call('client:notification', [message, type]);
    }
    /**
     * Send notification to all players
     */
    notifyAll(message, type = 'info') {
        this.getAllPlayers().forEach(player => {
            this.notify(player, message, type);
        });
    }
    /**
     * Get player count
     */
    getPlayerCount() {
        return this.players.size;
    }
    /**
     * Get online player count
     */
    getOnlinePlayerCount() {
        return this.getLoggedInPlayers().length;
    }
}
exports.PlayerManager = PlayerManager;
//# sourceMappingURL=PlayerManager.js.map