/**
 * Vehicle Manager
 * Handles all vehicle-related operations and management
 */
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';
export interface VehicleData {
    id: number;
    ownerId: number;
    model: string;
    position: {
        x: number;
        y: number;
        z: number;
    };
    heading: number;
    dimension: number;
    color1: {
        r: number;
        g: number;
        b: number;
    };
    color2: {
        r: number;
        g: number;
        b: number;
    };
    numberPlate: string;
    engineHealth: number;
    bodyHealth: number;
    fuel: number;
    mileage: number;
    locked: boolean;
    engineOn: boolean;
    modifications: any;
    insuranceExpires: Date | null;
    registrationExpires: Date | null;
    isImpounded: boolean;
    impoundReason: string | null;
}
export interface ExtendedVehicle extends VehicleMp {
    vehicleData?: VehicleData;
    lastDriver?: PlayerMp;
    lastPosition?: Vector3Mp;
    fuelConsumptionRate: number;
    lastFuelUpdate: Date;
    isBeingUsed: boolean;
}
export declare class VehicleManager {
    private vehicles;
    private databaseManager;
    private eventManager;
    private fuelUpdateInterval;
    constructor();
    /**
     * Set dependencies (called by ServerCore)
     */
    setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void;
    /**
     * Create a new vehicle
     */
    createVehicle(model: string, position: Vector3Mp, heading?: number, ownerId?: number, dimension?: number): Promise<ExtendedVehicle | null>;
    /**
     * Load vehicles from database
     */
    loadVehiclesFromDatabase(): Promise<void>;
    /**
     * Spawn vehicle from database data
     */
    private spawnVehicleFromData;
    /**
     * Get vehicle by ID
     */
    getVehicle(vehicleId: number): ExtendedVehicle | undefined;
    /**
     * Get vehicles by owner
     */
    getVehiclesByOwner(ownerId: number): ExtendedVehicle[];
    /**
     * Get all vehicles
     */
    getAllVehicles(): ExtendedVehicle[];
    /**
     * Save vehicle data to database
     */
    saveVehicle(vehicle: ExtendedVehicle): Promise<void>;
    /**
     * Save all vehicles
     */
    saveAllVehicles(): Promise<void>;
    /**
     * Delete vehicle
     */
    deleteVehicle(vehicle: ExtendedVehicle): Promise<void>;
    /**
     * Generate random number plate
     */
    private generateNumberPlate;
    /**
     * Extend vehicle object
     */
    private extendVehicle;
    /**
     * Calculate fuel consumption rate based on vehicle model
     */
    private calculateFuelConsumption;
    /**
     * Start fuel consumption system
     */
    private startFuelSystem;
    /**
     * Update fuel for all vehicles
     */
    private updateVehicleFuel;
    /**
     * Stop fuel system
     */
    stopFuelSystem(): void;
    /**
     * Get vehicle count
     */
    getVehicleCount(): number;
}
//# sourceMappingURL=VehicleManager.d.ts.map