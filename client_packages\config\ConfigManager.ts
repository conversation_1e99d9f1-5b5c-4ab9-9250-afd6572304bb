/**
 * Client-Side Configuration Manager
 * Handles client configuration settings
 */

import { Logger } from '../utils/Logger';

export interface ClientConfig {
    ui: {
        theme: 'dark' | 'light';
        language: string;
        animations: boolean;
        showFPS: boolean;
        showDebug: boolean;
    };
    graphics: {
        hudScale: number;
        chatScale: number;
        notificationDuration: number;
        maxNotifications: number;
    };
    audio: {
        masterVolume: number;
        musicVolume: number;
        sfxVolume: number;
        voiceVolume: number;
        ambientVolume: number;
    };
    controls: {
        mouseSensitivity: number;
        invertMouse: boolean;
        keyBindings: { [key: string]: number };
    };
    performance: {
        maxFPS: number;
        vsync: boolean;
        lowLatencyMode: boolean;
    };
}

export class ConfigManager {
    private config: ClientConfig;
    private configKey: string = 'ragemp_client_config';

    constructor() {
        this.setDefaultConfig();
    }

    /**
     * Load configuration
     */
    public async load(): Promise<void> {
        try {
            Logger.info('📋 Loading client configuration...');
            
            // Load from localStorage
            const savedConfig = localStorage.getItem(this.configKey);
            if (savedConfig) {
                const parsedConfig = JSON.parse(savedConfig);
                this.config = { ...this.config, ...parsedConfig };
                Logger.debug('Configuration loaded from localStorage');
            }
            
            Logger.success('✅ Client configuration loaded');
        } catch (error) {
            Logger.warn('⚠️ Could not load config, using defaults:', error);
        }
    }

    /**
     * Save configuration
     */
    public async save(): Promise<void> {
        try {
            Logger.info('💾 Saving client configuration...');
            
            localStorage.setItem(this.configKey, JSON.stringify(this.config));
            
            Logger.success('✅ Client configuration saved');
        } catch (error) {
            Logger.error('❌ Failed to save configuration:', error);
            throw error;
        }
    }

    /**
     * Set default configuration values
     */
    private setDefaultConfig(): void {
        this.config = {
            ui: {
                theme: 'dark',
                language: 'fa', // Persian
                animations: true,
                showFPS: false,
                showDebug: false
            },
            graphics: {
                hudScale: 1.0,
                chatScale: 1.0,
                notificationDuration: 5000,
                maxNotifications: 5
            },
            audio: {
                masterVolume: 1.0,
                musicVolume: 0.8,
                sfxVolume: 0.9,
                voiceVolume: 1.0,
                ambientVolume: 0.6
            },
            controls: {
                mouseSensitivity: 1.0,
                invertMouse: false,
                keyBindings: {
                    'toggleHUD': 115, // F4
                    'toggleChat': 114, // F3
                    'toggleDebug': 113, // F2
                    'screenshot': 121, // F10
                    'toggleFullscreen': 122 // F11
                }
            },
            performance: {
                maxFPS: 60,
                vsync: true,
                lowLatencyMode: false
            }
        };
    }

    /**
     * Get configuration value by path
     */
    public get<T>(path: string): T {
        const keys = path.split('.');
        let value: any = this.config;

        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                Logger.warn(`Configuration path not found: ${path}`);
                return undefined as T;
            }
        }

        return value as T;
    }

    /**
     * Set configuration value by path
     */
    public set(path: string, value: any): void {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let target: any = this.config;

        for (const key of keys) {
            if (!(key in target)) {
                target[key] = {};
            }
            target = target[key];
        }

        if (lastKey) {
            target[lastKey] = value;
            
            // Auto-save after setting a value
            this.save().catch(error => {
                Logger.error('Failed to auto-save config:', error);
            });
        }
    }

    /**
     * Get full configuration
     */
    public getAll(): ClientConfig {
        return { ...this.config };
    }

    /**
     * Update configuration with partial data
     */
    public update(partialConfig: Partial<ClientConfig>): void {
        this.config = this.mergeDeep(this.config, partialConfig);
        
        // Auto-save after update
        this.save().catch(error => {
            Logger.error('Failed to auto-save config after update:', error);
        });
    }

    /**
     * Reset to default configuration
     */
    public reset(): void {
        this.setDefaultConfig();
        this.save().catch(error => {
            Logger.error('Failed to save config after reset:', error);
        });
        Logger.info('🔄 Configuration reset to defaults');
    }

    /**
     * Validate configuration
     */
    public validate(): boolean {
        try {
            // Basic validation
            if (!this.config.ui || !this.config.graphics || !this.config.audio || !this.config.controls || !this.config.performance) {
                throw new Error('Missing required configuration sections');
            }

            // Validate ranges
            if (this.config.graphics.hudScale < 0.5 || this.config.graphics.hudScale > 2.0) {
                throw new Error('HUD scale must be between 0.5 and 2.0');
            }

            if (this.config.audio.masterVolume < 0 || this.config.audio.masterVolume > 1) {
                throw new Error('Master volume must be between 0 and 1');
            }

            Logger.success('✅ Configuration validation passed');
            return true;
        } catch (error) {
            Logger.error('❌ Configuration validation failed:', error);
            return false;
        }
    }

    /**
     * Deep merge objects
     */
    private mergeDeep(target: any, source: any): any {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.mergeDeep(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }

    /**
     * Export configuration as JSON string
     */
    public export(): string {
        return JSON.stringify(this.config, null, 2);
    }

    /**
     * Import configuration from JSON string
     */
    public import(configJson: string): boolean {
        try {
            const importedConfig = JSON.parse(configJson);
            this.config = { ...this.config, ...importedConfig };
            
            if (this.validate()) {
                this.save();
                Logger.success('✅ Configuration imported successfully');
                return true;
            } else {
                Logger.error('❌ Imported configuration failed validation');
                return false;
            }
        } catch (error) {
            Logger.error('❌ Failed to import configuration:', error);
            return false;
        }
    }

    /**
     * Get configuration for specific section
     */
    public getSection<K extends keyof ClientConfig>(section: K): ClientConfig[K] {
        return { ...this.config[section] };
    }

    /**
     * Update specific configuration section
     */
    public updateSection<K extends keyof ClientConfig>(section: K, data: Partial<ClientConfig[K]>): void {
        this.config[section] = { ...this.config[section], ...data };
        
        // Auto-save after section update
        this.save().catch(error => {
            Logger.error('Failed to auto-save config after section update:', error);
        });
    }
}
