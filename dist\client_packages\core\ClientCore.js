"use strict";
/**
 * Client Core - Main client management class
 * Handles initialization, module loading, and client lifecycle
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientCore = void 0;
const Logger_1 = require("../utils/Logger");
const EventManager_1 = require("../events/EventManager");
const UIManager_1 = require("../ui/UIManager");
const InputManager_1 = require("../input/InputManager");
const CameraManager_1 = require("../camera/CameraManager");
const AudioManager_1 = require("../audio/AudioManager");
const NetworkManager_1 = require("../network/NetworkManager");
const ConfigManager_1 = require("../config/ConfigManager");
class ClientCore {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        if (ClientCore.instance) {
            return ClientCore.instance;
        }
        ClientCore.instance = this;
    }
    /**
     * Initialize all client components
     */
    async start() {
        try {
            Logger_1.Logger.info('🔧 Initializing client core...');
            // Initialize configuration
            this.configManager = new ConfigManager_1.ConfigManager();
            await this.configManager.load();
            // Initialize event system
            this.eventManager = new EventManager_1.EventManager();
            // Initialize network manager
            this.networkManager = new NetworkManager_1.NetworkManager(this.eventManager);
            // Initialize managers
            this.uiManager = new UIManager_1.UIManager(this.eventManager);
            this.inputManager = new InputManager_1.InputManager(this.eventManager);
            this.cameraManager = new CameraManager_1.CameraManager(this.eventManager);
            this.audioManager = new AudioManager_1.AudioManager(this.eventManager);
            // Register all events
            this.registerEvents();
            // Initialize all managers
            await this.initializeManagers();
            this.isInitialized = true;
            this.isRunning = true;
            Logger_1.Logger.success('✅ Client core initialized successfully');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Failed to initialize client core:', error);
            throw error;
        }
    }
    /**
     * Shutdown client gracefully
     */
    async shutdown() {
        if (!this.isRunning)
            return;
        Logger_1.Logger.info('🛑 Shutting down client core...');
        try {
            // Shutdown all managers
            await this.shutdownManagers();
            this.isRunning = false;
            Logger_1.Logger.success('✅ Client shutdown completed');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Error during shutdown:', error);
        }
    }
    /**
     * Initialize all managers
     */
    async initializeManagers() {
        try {
            await this.networkManager.initialize();
            await this.uiManager.initialize();
            await this.inputManager.initialize();
            await this.cameraManager.initialize();
            await this.audioManager.initialize();
            Logger_1.Logger.success('✅ All managers initialized');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Failed to initialize managers:', error);
            throw error;
        }
    }
    /**
     * Shutdown all managers
     */
    async shutdownManagers() {
        try {
            await this.audioManager.shutdown();
            await this.cameraManager.shutdown();
            await this.inputManager.shutdown();
            await this.uiManager.shutdown();
            await this.networkManager.shutdown();
            Logger_1.Logger.success('✅ All managers shut down');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Error shutting down managers:', error);
        }
    }
    /**
     * Register all client events
     */
    registerEvents() {
        // RAGE MP Events
        mp.events.add('render', this.onRender.bind(this));
        mp.events.add('browserCreated', this.onBrowserCreated.bind(this));
        mp.events.add('browserLoadingFailed', this.onBrowserLoadingFailed.bind(this));
        // Custom client events
        mp.events.add('client:notification', this.onNotification.bind(this));
        mp.events.add('client:showLoginForm', this.onShowLoginForm.bind(this));
        mp.events.add('client:showRegisterForm', this.onShowRegisterForm.bind(this));
        mp.events.add('client:authError', this.onAuthError.bind(this));
        mp.events.add('client:showCharacterSelection', this.onShowCharacterSelection.bind(this));
        mp.events.add('client:characterLoaded', this.onCharacterLoaded.bind(this));
        mp.events.add('client:characterError', this.onCharacterError.bind(this));
        mp.events.add('client:applySkinData', this.onApplySkinData.bind(this));
        mp.events.add('client:applyClothes', this.onApplyClothes.bind(this));
        Logger_1.Logger.debug('Client events registered');
    }
    /**
     * Handle render event
     */
    onRender() {
        // This is called every frame
        // Update managers that need per-frame updates
        if (this.isRunning) {
            this.uiManager.update();
            this.cameraManager.update();
        }
    }
    /**
     * Handle browser created
     */
    onBrowserCreated(browser) {
        Logger_1.Logger.debug('Browser created:', browser.id);
        this.eventManager.emit('browser:created', browser);
    }
    /**
     * Handle browser loading failed
     */
    onBrowserLoadingFailed(browser, url, errorCode, errorDesc) {
        Logger_1.Logger.error(`Browser loading failed: ${url} (${errorCode}: ${errorDesc})`);
        this.eventManager.emit('browser:loadingFailed', browser, url, errorCode, errorDesc);
    }
    /**
     * Handle notification
     */
    onNotification(message, type = 'info') {
        this.uiManager.showNotification(message, type);
    }
    /**
     * Handle show login form
     */
    onShowLoginForm(username) {
        this.uiManager.showLoginForm(username);
    }
    /**
     * Handle show register form
     */
    onShowRegisterForm() {
        this.uiManager.showRegisterForm();
    }
    /**
     * Handle authentication error
     */
    onAuthError(message) {
        this.uiManager.showAuthError(message);
    }
    /**
     * Handle show character selection
     */
    onShowCharacterSelection(characters) {
        this.uiManager.showCharacterSelection(characters);
    }
    /**
     * Handle character loaded
     */
    onCharacterLoaded(characterData) {
        this.uiManager.onCharacterLoaded(characterData);
        this.eventManager.emit('character:loaded', characterData);
    }
    /**
     * Handle character error
     */
    onCharacterError(message) {
        this.uiManager.showCharacterError(message);
    }
    /**
     * Handle apply skin data
     */
    onApplySkinData(skinData) {
        // Apply skin customization to local player
        // This would involve RAGE MP native calls
        Logger_1.Logger.debug('Applying skin data:', skinData);
    }
    /**
     * Handle apply clothes
     */
    onApplyClothes(clothesData) {
        // Apply clothes to local player
        // This would involve RAGE MP native calls
        Logger_1.Logger.debug('Applying clothes:', clothesData);
    }
    // Getters for accessing managers
    static getInstance() {
        return ClientCore.instance;
    }
    getEventManager() {
        return this.eventManager;
    }
    getUIManager() {
        return this.uiManager;
    }
    getInputManager() {
        return this.inputManager;
    }
    getCameraManager() {
        return this.cameraManager;
    }
    getAudioManager() {
        return this.audioManager;
    }
    getNetworkManager() {
        return this.networkManager;
    }
    getConfigManager() {
        return this.configManager;
    }
    /**
     * Check if client is running
     */
    isClientRunning() {
        return this.isRunning;
    }
    /**
     * Check if client is initialized
     */
    isClientInitialized() {
        return this.isInitialized;
    }
}
exports.ClientCore = ClientCore;
//# sourceMappingURL=ClientCore.js.map