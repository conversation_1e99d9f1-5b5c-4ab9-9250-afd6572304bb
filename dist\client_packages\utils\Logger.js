"use strict";
/**
 * Client-Side Logger System
 * Provides comprehensive logging functionality for client-side operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
    LogLevel[LogLevel["SUCCESS"] = 4] = "SUCCESS";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    /**
     * Set the minimum log level
     */
    static setLogLevel(level) {
        this.logLevel = level;
    }
    /**
     * Enable/disable console output
     */
    static setConsoleOutput(enabled) {
        this.enableConsole = enabled;
    }
    /**
     * Debug level logging
     */
    static debug(message, data, source) {
        this.log(LogLevel.DEBUG, message, data, source);
    }
    /**
     * Info level logging
     */
    static info(message, data, source) {
        this.log(LogLevel.INFO, message, data, source);
    }
    /**
     * Warning level logging
     */
    static warn(message, data, source) {
        this.log(LogLevel.WARN, message, data, source);
    }
    /**
     * Error level logging
     */
    static error(message, data, source) {
        this.log(LogLevel.ERROR, message, data, source);
    }
    /**
     * Success level logging
     */
    static success(message, data, source) {
        this.log(LogLevel.SUCCESS, message, data, source);
    }
    /**
     * Core logging method
     */
    static log(level, message, data, source) {
        if (level < this.logLevel)
            return;
        const timestamp = new Date();
        const logEntry = {
            timestamp,
            level,
            message,
            data,
            source
        };
        // Add to logs array
        this.logs.push(logEntry);
        // Maintain max logs limit
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        // Output to console if enabled
        if (this.enableConsole) {
            this.outputToConsole(logEntry);
        }
        // Send to server for persistent logging (optional)
        this.sendToServer(logEntry);
    }
    /**
     * Output log to browser console
     */
    static outputToConsole(entry) {
        const timestamp = entry.timestamp.toISOString().replace('T', ' ').substring(0, 19);
        const levelStr = this.getLevelString(entry.level);
        const sourceStr = entry.source ? ` [${entry.source}]` : '';
        let message = `[${timestamp}] ${levelStr}${sourceStr} ${entry.message}`;
        if (entry.data) {
            message += ` | Data:`;
        }
        // Use appropriate console method based on level
        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(message, entry.data || '');
                break;
            case LogLevel.INFO:
                console.info(message, entry.data || '');
                break;
            case LogLevel.WARN:
                console.warn(message, entry.data || '');
                break;
            case LogLevel.ERROR:
                console.error(message, entry.data || '');
                break;
            case LogLevel.SUCCESS:
                console.log(message, entry.data || '');
                break;
            default:
                console.log(message, entry.data || '');
        }
    }
    /**
     * Send log to server (optional)
     */
    static sendToServer(entry) {
        // Only send important logs to server to avoid spam
        if (entry.level >= LogLevel.WARN) {
            try {
                mp.events.callRemote('server:clientLog', JSON.stringify({
                    level: LogLevel[entry.level],
                    message: entry.message,
                    data: entry.data,
                    source: entry.source,
                    timestamp: entry.timestamp.toISOString()
                }));
            }
            catch (error) {
                // Silently fail if server communication is not available
            }
        }
    }
    /**
     * Get level string
     */
    static getLevelString(level) {
        switch (level) {
            case LogLevel.DEBUG:
                return '[DEBUG]';
            case LogLevel.INFO:
                return '[INFO]';
            case LogLevel.WARN:
                return '[WARN]';
            case LogLevel.ERROR:
                return '[ERROR]';
            case LogLevel.SUCCESS:
                return '[SUCCESS]';
            default:
                return '[UNKNOWN]';
        }
    }
    /**
     * Get all logs
     */
    static getLogs() {
        return [...this.logs];
    }
    /**
     * Get logs by level
     */
    static getLogsByLevel(level) {
        return this.logs.filter(log => log.level === level);
    }
    /**
     * Clear all logs
     */
    static clearLogs() {
        this.logs = [];
    }
    /**
     * Export logs to string
     */
    static exportLogs() {
        return this.logs.map(log => {
            const timestamp = log.timestamp.toISOString().replace('T', ' ').substring(0, 19);
            const levelStr = this.getLevelString(log.level);
            const sourceStr = log.source ? ` [${log.source}]` : '';
            let message = `[${timestamp}] ${levelStr}${sourceStr} ${log.message}`;
            if (log.data) {
                message += ` | Data: ${JSON.stringify(log.data)}`;
            }
            return message;
        }).join('\n');
    }
    /**
     * Get logs for UI display
     */
    static getLogsForUI(limit = 100) {
        return this.logs.slice(-limit);
    }
    /**
     * Filter logs by search term
     */
    static searchLogs(searchTerm) {
        const term = searchTerm.toLowerCase();
        return this.logs.filter(log => log.message.toLowerCase().includes(term) ||
            (log.source && log.source.toLowerCase().includes(term)) ||
            (log.data && JSON.stringify(log.data).toLowerCase().includes(term)));
    }
    /**
     * Get log statistics
     */
    static getLogStats() {
        const stats = {
            total: this.logs.length,
            debug: 0,
            info: 0,
            warn: 0,
            error: 0,
            success: 0
        };
        this.logs.forEach(log => {
            switch (log.level) {
                case LogLevel.DEBUG:
                    stats.debug++;
                    break;
                case LogLevel.INFO:
                    stats.info++;
                    break;
                case LogLevel.WARN:
                    stats.warn++;
                    break;
                case LogLevel.ERROR:
                    stats.error++;
                    break;
                case LogLevel.SUCCESS:
                    stats.success++;
                    break;
            }
        });
        return stats;
    }
}
exports.Logger = Logger;
Logger.logLevel = LogLevel.INFO;
Logger.logs = [];
Logger.maxLogs = 500; // Smaller limit for client
Logger.enableConsole = true;
//# sourceMappingURL=Logger.js.map