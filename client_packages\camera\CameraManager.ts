/**
 * Camera Manager
 * Handles camera operations and management
 */

import { Logger } from '../utils/Logger';
import { EventManager } from '../events/EventManager';

export interface CameraState {
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
    fov: number;
    isActive: boolean;
}

export class CameraManager {
    private eventManager: EventManager;
    private currentCamera: any = null;
    private cameraState: CameraState;

    constructor(eventManager: EventManager) {
        this.eventManager = eventManager;
        this.cameraState = {
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            fov: 60,
            isActive: false
        };
    }

    /**
     * Initialize Camera Manager
     */
    public async initialize(): Promise<void> {
        try {
            Logger.info('📷 Initializing Camera Manager...');

            // Register camera events
            this.registerEvents();

            Logger.success('✅ Camera Manager initialized');
        } catch (error) {
            Logger.error('❌ Failed to initialize Camera Manager:', error);
            throw error;
        }
    }

    /**
     * Shutdown Camera Manager
     */
    public async shutdown(): Promise<void> {
        try {
            Logger.info('🛑 Shutting down Camera Manager...');
            
            // Destroy current camera
            if (this.currentCamera) {
                this.destroyCamera();
            }
            
            Logger.success('✅ Camera Manager shut down');
        } catch (error) {
            Logger.error('❌ Error shutting down Camera Manager:', error);
        }
    }

    /**
     * Update camera (called every frame)
     */
    public update(): void {
        // Update camera state if needed
        if (this.currentCamera && this.cameraState.isActive) {
            // Perform any per-frame camera updates here
        }
    }

    /**
     * Register camera events
     */
    private registerEvents(): void {
        this.eventManager.on('camera:create', this.onCreateCamera.bind(this));
        this.eventManager.on('camera:destroy', this.onDestroyCamera.bind(this));
        this.eventManager.on('camera:setPosition', this.onSetPosition.bind(this));
        this.eventManager.on('camera:setRotation', this.onSetRotation.bind(this));
        this.eventManager.on('camera:setFOV', this.onSetFOV.bind(this));

        Logger.debug('Camera events registered');
    }

    /**
     * Create a new camera
     */
    public createCamera(position: { x: number; y: number; z: number }, rotation: { x: number; y: number; z: number }, fov: number = 60): void {
        try {
            // Destroy existing camera
            if (this.currentCamera) {
                this.destroyCamera();
            }

            // Create new camera using RAGE MP natives
            if (typeof mp !== 'undefined' && mp.game && mp.game.cam) {
                this.currentCamera = mp.game.cam.createCamWithParams(
                    'DEFAULT_SCRIPTED_CAMERA',
                    position.x, position.y, position.z,
                    rotation.x, rotation.y, rotation.z,
                    fov, true, 2
                );

                if (this.currentCamera) {
                    mp.game.cam.setCamActive(this.currentCamera, true);
                    mp.game.cam.renderScriptCams(true, false, 0, true, false);

                    this.cameraState = {
                        position,
                        rotation,
                        fov,
                        isActive: true
                    };

                    Logger.debug('Camera created and activated');
                }
            }
        } catch (error) {
            Logger.error('Failed to create camera:', error);
        }
    }

    /**
     * Destroy current camera
     */
    public destroyCamera(): void {
        try {
            if (this.currentCamera) {
                if (typeof mp !== 'undefined' && mp.game && mp.game.cam) {
                    mp.game.cam.renderScriptCams(false, false, 0, true, false);
                    mp.game.cam.setCamActive(this.currentCamera, false);
                    mp.game.cam.destroyCam(this.currentCamera, false);
                }

                this.currentCamera = null;
                this.cameraState.isActive = false;

                Logger.debug('Camera destroyed');
            }
        } catch (error) {
            Logger.error('Failed to destroy camera:', error);
        }
    }

    /**
     * Set camera position
     */
    public setPosition(x: number, y: number, z: number): void {
        if (this.currentCamera && typeof mp !== 'undefined' && mp.game && mp.game.cam) {
            mp.game.cam.setCamCoord(this.currentCamera, x, y, z);
            this.cameraState.position = { x, y, z };
            Logger.debug(`Camera position set to: ${x}, ${y}, ${z}`);
        }
    }

    /**
     * Set camera rotation
     */
    public setRotation(x: number, y: number, z: number): void {
        if (this.currentCamera && typeof mp !== 'undefined' && mp.game && mp.game.cam) {
            mp.game.cam.setCamRot(this.currentCamera, x, y, z, 2);
            this.cameraState.rotation = { x, y, z };
            Logger.debug(`Camera rotation set to: ${x}, ${y}, ${z}`);
        }
    }

    /**
     * Set camera FOV
     */
    public setFOV(fov: number): void {
        if (this.currentCamera && typeof mp !== 'undefined' && mp.game && mp.game.cam) {
            mp.game.cam.setCamFov(this.currentCamera, fov);
            this.cameraState.fov = fov;
            Logger.debug(`Camera FOV set to: ${fov}`);
        }
    }

    /**
     * Event handlers
     */
    private onCreateCamera(data: { position: { x: number; y: number; z: number }; rotation: { x: number; y: number; z: number }; fov?: number }): void {
        this.createCamera(data.position, data.rotation, data.fov);
    }

    private onDestroyCamera(): void {
        this.destroyCamera();
    }

    private onSetPosition(data: { x: number; y: number; z: number }): void {
        this.setPosition(data.x, data.y, data.z);
    }

    private onSetRotation(data: { x: number; y: number; z: number }): void {
        this.setRotation(data.x, data.y, data.z);
    }

    private onSetFOV(data: { fov: number }): void {
        this.setFOV(data.fov);
    }

    /**
     * Get current camera state
     */
    public getCameraState(): CameraState {
        return { ...this.cameraState };
    }

    /**
     * Check if camera is active
     */
    public isCameraActive(): boolean {
        return this.cameraState.isActive;
    }
}
