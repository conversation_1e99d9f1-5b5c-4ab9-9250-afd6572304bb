/**
 * Vehicle Manager
 * Handles all vehicle-related operations and management
 */

import { Logger } from '../utils/Logger';
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';

export interface VehicleData {
    id: number;
    ownerId: number;
    model: string;
    position: { x: number; y: number; z: number };
    heading: number;
    dimension: number;
    color1: { r: number; g: number; b: number };
    color2: { r: number; g: number; b: number };
    numberPlate: string;
    engineHealth: number;
    bodyHealth: number;
    fuel: number;
    mileage: number;
    locked: boolean;
    engineOn: boolean;
    modifications: any;
    insuranceExpires: Date | null;
    registrationExpires: Date | null;
    isImpounded: boolean;
    impoundReason: string | null;
}

export interface ExtendedVehicle extends VehicleMp {
    vehicleData?: VehicleData;
    lastDriver?: PlayerMp;
    lastPosition?: Vector3Mp;
    fuelConsumptionRate: number;
    lastFuelUpdate: Date;
    isBeingUsed: boolean;
}

export class VehicleManager {
    private vehicles: Map<number, ExtendedVehicle> = new Map();
    private databaseManager: DatabaseManager;
    private eventManager: EventManager;
    private fuelUpdateInterval: NodeJS.Timeout | null = null;

    constructor() {
        this.databaseManager = new DatabaseManager();
        this.eventManager = new EventManager();
        this.startFuelSystem();
    }

    /**
     * Set dependencies (called by ServerCore)
     */
    public setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void {
        this.databaseManager = databaseManager;
        this.eventManager = eventManager;
    }

    /**
     * Create a new vehicle
     */
    public async createVehicle(
        model: string,
        position: Vector3Mp,
        heading: number = 0,
        ownerId?: number,
        dimension: number = 0
    ): Promise<ExtendedVehicle | null> {
        try {
            // Create vehicle in game world
            const vehicle = mp.vehicles.new(mp.joaat(model), position, {
                heading,
                dimension,
                numberPlate: this.generateNumberPlate(),
                color: [[255, 255, 255], [255, 255, 255]],
                locked: true,
                engine: false
            });

            if (!vehicle) {
                Logger.error('Failed to create vehicle in game world');
                return null;
            }

            // Extend vehicle object
            const extendedVehicle = this.extendVehicle(vehicle);

            // Create vehicle data
            const vehicleData: Partial<VehicleData> = {
                model,
                position: { x: position.x, y: position.y, z: position.z },
                heading,
                dimension,
                color1: { r: 255, g: 255, b: 255 },
                color2: { r: 255, g: 255, b: 255 },
                numberPlate: vehicle.numberPlate,
                engineHealth: 1000,
                bodyHealth: 1000,
                fuel: 100,
                mileage: 0,
                locked: true,
                engineOn: false,
                modifications: {},
                isImpounded: false
            };

            if (ownerId) {
                vehicleData.ownerId = ownerId;
            }

            // Save to database if owner is specified
            if (ownerId) {
                const result = await this.databaseManager.execute(
                    `INSERT INTO vehicles (
                        owner_id, model, position_x, position_y, position_z, heading, dimension,
                        color1, color2, number_plate, engine_health, body_health, fuel, mileage,
                        locked, engine_on, modifications, is_impounded
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        ownerId, model, position.x, position.y, position.z, heading, dimension,
                        JSON.stringify(vehicleData.color1), JSON.stringify(vehicleData.color2),
                        vehicle.numberPlate, vehicleData.engineHealth, vehicleData.bodyHealth,
                        vehicleData.fuel, vehicleData.mileage, vehicleData.locked, vehicleData.engineOn,
                        JSON.stringify(vehicleData.modifications), vehicleData.isImpounded
                    ]
                );

                if (result && result.insertId) {
                    vehicleData.id = result.insertId;
                }
            }

            extendedVehicle.vehicleData = vehicleData as VehicleData;
            this.vehicles.set(vehicle.id, extendedVehicle);

            Logger.info(`🚗 Vehicle created: ${model} (ID: ${vehicle.id})`);
            await this.eventManager.emit('vehicle:created', extendedVehicle);

            return extendedVehicle;
        } catch (error) {
            Logger.error('Failed to create vehicle:', error);
            return null;
        }
    }

    /**
     * Load vehicles from database
     */
    public async loadVehiclesFromDatabase(): Promise<void> {
        try {
            Logger.info('🚗 Loading vehicles from database...');

            const vehicleData = await this.databaseManager.execute(
                'SELECT * FROM vehicles WHERE is_impounded = FALSE'
            );

            if (!vehicleData || vehicleData.length === 0) {
                Logger.info('No vehicles found in database');
                return;
            }

            for (const data of vehicleData) {
                await this.spawnVehicleFromData(data);
            }

            Logger.success(`✅ Loaded ${vehicleData.length} vehicles from database`);
        } catch (error) {
            Logger.error('Failed to load vehicles from database:', error);
        }
    }

    /**
     * Spawn vehicle from database data
     */
    private async spawnVehicleFromData(data: any): Promise<ExtendedVehicle | null> {
        try {
            const position = new mp.Vector3(data.position_x, data.position_y, data.position_z);
            
            const vehicle = mp.vehicles.new(mp.joaat(data.model), position, {
                heading: data.heading,
                dimension: data.dimension,
                numberPlate: data.number_plate,
                color: [JSON.parse(data.color1), JSON.parse(data.color2)],
                locked: data.locked,
                engine: data.engine_on
            });

            if (!vehicle) {
                Logger.error(`Failed to spawn vehicle from database: ${data.model}`);
                return null;
            }

            const extendedVehicle = this.extendVehicle(vehicle);
            extendedVehicle.vehicleData = {
                id: data.id,
                ownerId: data.owner_id,
                model: data.model,
                position: { x: data.position_x, y: data.position_y, z: data.position_z },
                heading: data.heading,
                dimension: data.dimension,
                color1: JSON.parse(data.color1),
                color2: JSON.parse(data.color2),
                numberPlate: data.number_plate,
                engineHealth: data.engine_health,
                bodyHealth: data.body_health,
                fuel: data.fuel,
                mileage: data.mileage,
                locked: data.locked,
                engineOn: data.engine_on,
                modifications: JSON.parse(data.modifications || '{}'),
                insuranceExpires: data.insurance_expires,
                registrationExpires: data.registration_expires,
                isImpounded: data.is_impounded,
                impoundReason: data.impound_reason
            };

            this.vehicles.set(vehicle.id, extendedVehicle);
            return extendedVehicle;
        } catch (error) {
            Logger.error('Failed to spawn vehicle from data:', error);
            return null;
        }
    }

    /**
     * Get vehicle by ID
     */
    public getVehicle(vehicleId: number): ExtendedVehicle | undefined {
        return this.vehicles.get(vehicleId);
    }

    /**
     * Get vehicles by owner
     */
    public getVehiclesByOwner(ownerId: number): ExtendedVehicle[] {
        return Array.from(this.vehicles.values()).filter(
            vehicle => vehicle.vehicleData?.ownerId === ownerId
        );
    }

    /**
     * Get all vehicles
     */
    public getAllVehicles(): ExtendedVehicle[] {
        return Array.from(this.vehicles.values());
    }

    /**
     * Save vehicle data to database
     */
    public async saveVehicle(vehicle: ExtendedVehicle): Promise<void> {
        if (!vehicle.vehicleData || !vehicle.vehicleData.id) return;

        try {
            await this.databaseManager.execute(
                `UPDATE vehicles SET 
                 position_x = ?, position_y = ?, position_z = ?, heading = ?,
                 engine_health = ?, body_health = ?, fuel = ?, mileage = ?,
                 locked = ?, engine_on = ?, modifications = ?
                 WHERE id = ?`,
                [
                    vehicle.position.x, vehicle.position.y, vehicle.position.z, vehicle.heading,
                    vehicle.vehicleData.engineHealth, vehicle.vehicleData.bodyHealth,
                    vehicle.vehicleData.fuel, vehicle.vehicleData.mileage,
                    vehicle.vehicleData.locked, vehicle.vehicleData.engineOn,
                    JSON.stringify(vehicle.vehicleData.modifications),
                    vehicle.vehicleData.id
                ]
            );

            Logger.debug(`Saved vehicle data: ${vehicle.vehicleData.model} (${vehicle.id})`);
        } catch (error) {
            Logger.error(`Failed to save vehicle ${vehicle.id}:`, error);
        }
    }

    /**
     * Save all vehicles
     */
    public async saveAllVehicles(): Promise<void> {
        Logger.info('💾 Saving all vehicles...');
        
        const savePromises = this.getAllVehicles().map(vehicle =>
            this.saveVehicle(vehicle).catch(error =>
                Logger.error(`Failed to save vehicle ${vehicle.id}:`, error)
            )
        );

        await Promise.all(savePromises);
        Logger.success('✅ All vehicles saved');
    }

    /**
     * Delete vehicle
     */
    public async deleteVehicle(vehicle: ExtendedVehicle): Promise<void> {
        try {
            // Remove from database
            if (vehicle.vehicleData?.id) {
                await this.databaseManager.execute(
                    'DELETE FROM vehicles WHERE id = ?',
                    [vehicle.vehicleData.id]
                );
            }

            // Remove from game world
            this.vehicles.delete(vehicle.id);
            vehicle.destroy();

            Logger.info(`🗑️ Vehicle deleted: ${vehicle.vehicleData?.model} (${vehicle.id})`);
            await this.eventManager.emit('vehicle:deleted', vehicle);
        } catch (error) {
            Logger.error(`Failed to delete vehicle ${vehicle.id}:`, error);
        }
    }

    /**
     * Generate random number plate
     */
    private generateNumberPlate(): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * Extend vehicle object
     */
    private extendVehicle(vehicle: VehicleMp): ExtendedVehicle {
        const extendedVehicle = vehicle as ExtendedVehicle;
        
        extendedVehicle.fuelConsumptionRate = this.calculateFuelConsumption(vehicle.model);
        extendedVehicle.lastFuelUpdate = new Date();
        extendedVehicle.isBeingUsed = false;
        extendedVehicle.lastPosition = vehicle.position;

        return extendedVehicle;
    }

    /**
     * Calculate fuel consumption rate based on vehicle model
     */
    private calculateFuelConsumption(model: number): number {
        // Base consumption rate (liters per minute while driving)
        // This would be more sophisticated in a real implementation
        return 0.5; // 0.5 liters per minute
    }

    /**
     * Start fuel consumption system
     */
    private startFuelSystem(): void {
        this.fuelUpdateInterval = setInterval(() => {
            this.updateVehicleFuel();
        }, 60000); // Update every minute

        Logger.info('⛽ Fuel system started');
    }

    /**
     * Update fuel for all vehicles
     */
    private updateVehicleFuel(): void {
        for (const vehicle of this.vehicles.values()) {
            if (vehicle.engine && vehicle.vehicleData) {
                const timeDiff = (Date.now() - vehicle.lastFuelUpdate.getTime()) / 60000; // minutes
                const fuelConsumed = vehicle.fuelConsumptionRate * timeDiff;
                
                vehicle.vehicleData.fuel = Math.max(0, vehicle.vehicleData.fuel - fuelConsumed);
                vehicle.lastFuelUpdate = new Date();

                // Turn off engine if out of fuel
                if (vehicle.vehicleData.fuel <= 0 && vehicle.engine) {
                    vehicle.engine = false;
                    vehicle.vehicleData.engineOn = false;
                    
                    // Notify occupants
                    const driver = vehicle.getOccupant(0);
                    if (driver) {
                        mp.events.call('client:notification', driver, ['Vehicle ran out of fuel!', 'error']);
                    }
                }
            }
        }
    }

    /**
     * Stop fuel system
     */
    public stopFuelSystem(): void {
        if (this.fuelUpdateInterval) {
            clearInterval(this.fuelUpdateInterval);
            this.fuelUpdateInterval = null;
            Logger.info('⛽ Fuel system stopped');
        }
    }

    /**
     * Get vehicle count
     */
    public getVehicleCount(): number {
        return this.vehicles.size;
    }
}
