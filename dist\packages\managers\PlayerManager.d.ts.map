{"version": 3, "file": "PlayerManager.d.ts", "sourceRoot": "", "sources": ["../../../packages/managers/PlayerManager.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAGH,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,MAAM,WAAW,UAAU;IACvB,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,gBAAgB,EAAE,IAAI,CAAC;IACvB,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC;IACvB,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,EAAE,IAAI,GAAG,IAAI,CAAC;IACzB,QAAQ,EAAE,OAAO,CAAC;IAClB,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,WAAW,aAAa;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,GAAG,QAAQ,CAAC;IAC1B,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAC9C,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,GAAG,EAAE,MAAM,CAAC;IACZ,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,GAAG,CAAC;IACd,WAAW,EAAE,GAAG,CAAC;IACjB,QAAQ,EAAE,OAAO,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,cAAe,SAAQ,QAAQ;IAC5C,QAAQ,CAAC,EAAE,UAAU,CAAC;IACtB,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,UAAU,EAAE,OAAO,CAAC;IACpB,mBAAmB,EAAE,OAAO,CAAC;IAC7B,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,IAAI,CAAC;IACnB,gBAAgB,EAAE,IAAI,CAAC;CAC1B;AAED,qBAAa,aAAa;IACtB,OAAO,CAAC,OAAO,CAA0C;IACzD,OAAO,CAAC,eAAe,CAAkB;IACzC,OAAO,CAAC,YAAY,CAAe;;IAQnC;;OAEG;IACI,eAAe,CAAC,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,GAAG,IAAI;IAK1F;;OAEG;IACU,YAAY,CAAC,MAAM,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAqB1D;;OAEG;IACU,YAAY,CAAC,MAAM,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IA0B1D;;OAEG;IACI,SAAS,CAAC,QAAQ,EAAE,MAAM,GAAG,cAAc,GAAG,SAAS;IAI9D;;OAEG;IACI,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,cAAc,GAAG,SAAS;IAShE;;OAEG;IACI,aAAa,IAAI,cAAc,EAAE;IAIxC;;OAEG;IACI,kBAAkB,IAAI,cAAc,EAAE;IAI7C;;OAEG;IACI,gBAAgB,IAAI,cAAc,EAAE;IAI3C;;OAEG;IACU,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;IAa5C;;OAEG;YACW,cAAc;IAkB5B;;OAEG;YACW,cAAc;IA2C5B;;OAEG;YACW,cAAc;IAiB5B;;OAEG;IACH,OAAO,CAAC,YAAY;IAYpB;;OAEG;IACH,OAAO,CAAC,eAAe;IAUvB;;OAEG;IACI,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,MAAM,GAAE,MAA8B,GAAG,IAAI;IAKvF;;OAEG;IACU,SAAS,CAAC,MAAM,EAAE,cAAc,EAAE,MAAM,GAAE,MAA8B,EAAE,SAAS,GAAE,MAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAsBnI;;OAEG;IACI,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,GAAE,MAAe,GAAG,IAAI;IAInF;;OAEG;IACI,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,GAAE,MAAe,GAAG,IAAI;IAM9D;;OAEG;IACI,cAAc,IAAI,MAAM;IAI/B;;OAEG;IACI,oBAAoB,IAAI,MAAM;CAGxC"}