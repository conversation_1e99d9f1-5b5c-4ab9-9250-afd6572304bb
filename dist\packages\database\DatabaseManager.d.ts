/**
 * Database Manager
 * Handles all database connections and operations
 */
interface Connection {
    execute(query: string, params?: any[]): Promise<any>;
    end(): Promise<void>;
}
export declare class DatabaseManager {
    private pool;
    private configManager;
    private isConnected;
    constructor();
    /**
     * Connect to the database
     */
    connect(): Promise<void>;
    /**
     * Disconnect from the database
     */
    disconnect(): Promise<void>;
    /**
     * Test database connection
     */
    private testConnection;
    /**
     * Initialize database schema
     */
    private initializeSchema;
    /**
     * Execute a database query
     */
    execute(query: string, params?: any[]): Promise<any>;
    /**
     * Get a database connection from the pool
     */
    getConnection(): Promise<Connection>;
    /**
     * Create users table
     */
    private createUsersTable;
    /**
     * Create characters table
     */
    private createCharactersTable;
    /**
     * Create vehicles table
     */
    private createVehiclesTable;
    /**
     * Create properties table
     */
    private createPropertiesTable;
    /**
     * Create businesses table
     */
    private createBusinessesTable;
    /**
     * Create inventory table
     */
    private createInventoryTable;
    /**
     * Create bank accounts table
     */
    private createBankAccountsTable;
    /**
     * Create log table
     */
    private createLogTable;
    /**
     * Create a mock pool for development
     * TODO: Replace with actual MySQL2 implementation
     */
    private createMockPool;
    /**
     * Check if database is connected
     */
    isConnectedToDatabase(): boolean;
}
export {};
//# sourceMappingURL=DatabaseManager.d.ts.map