/**
 * Theme Context Provider
 * Provides theme configuration and styling to React components
 */
import React, { ReactNode } from 'react';
export interface Theme {
    name: string;
    colors: {
        primary: string;
        secondary: string;
        accent: string;
        background: string;
        surface: string;
        text: {
            primary: string;
            secondary: string;
            disabled: string;
        };
        status: {
            success: string;
            warning: string;
            error: string;
            info: string;
        };
        border: string;
        shadow: string;
    };
    typography: {
        fontFamily: string;
        fontSize: {
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            xxl: string;
        };
        fontWeight: {
            light: number;
            normal: number;
            medium: number;
            bold: number;
        };
    };
    spacing: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        xxl: string;
    };
    borderRadius: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        full: string;
    };
    animation: {
        duration: {
            fast: string;
            normal: string;
            slow: string;
        };
        easing: {
            ease: string;
            easeIn: string;
            easeOut: string;
            easeInOut: string;
        };
    };
}
declare const darkTheme: Theme;
declare const lightTheme: Theme;
export interface ThemeContextType {
    theme: Theme;
    themeName: 'dark' | 'light';
    setTheme: (themeName: 'dark' | 'light') => void;
    toggleTheme: () => void;
}
declare const ThemeContext: React.Context<ThemeContextType | null>;
export declare const ThemeProvider: React.FC<{
    children: ReactNode;
}>;
export declare const useTheme: () => ThemeContextType;
export { darkTheme, lightTheme };
export default ThemeContext;
//# sourceMappingURL=ThemeContext.d.ts.map