{"version": 3, "file": "ClientCore.d.ts", "sourceRoot": "", "sources": ["../../../client_packages/core/ClientCore.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAGH,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,qBAAa,UAAU;IACnB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAa;IACpC,OAAO,CAAC,aAAa,CAAkB;IACvC,OAAO,CAAC,SAAS,CAAkB;IAGnC,OAAO,CAAC,YAAY,CAAe;IACnC,OAAO,CAAC,SAAS,CAAY;IAC7B,OAAO,CAAC,YAAY,CAAe;IACnC,OAAO,CAAC,aAAa,CAAgB;IACrC,OAAO,CAAC,YAAY,CAAe;IACnC,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,aAAa,CAAgB;;IASrC;;OAEG;IACU,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAoCnC;;OAEG;IACU,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAgBtC;;OAEG;YACW,kBAAkB;IAehC;;OAEG;YACW,gBAAgB;IAc9B;;OAEG;IACH,OAAO,CAAC,cAAc;IAoBtB;;OAEG;IACH,OAAO,CAAC,QAAQ;IAShB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAKxB;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAK9B;;OAEG;IACH,OAAO,CAAC,cAAc;IAItB;;OAEG;IACH,OAAO,CAAC,eAAe;IAIvB;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAI1B;;OAEG;IACH,OAAO,CAAC,WAAW;IAInB;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAIhC;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAKzB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAIxB;;OAEG;IACH,OAAO,CAAC,eAAe;IAMvB;;OAEG;IACH,OAAO,CAAC,cAAc;WAOR,WAAW,IAAI,UAAU;IAIhC,eAAe,IAAI,YAAY;IAI/B,YAAY,IAAI,SAAS;IAIzB,eAAe,IAAI,YAAY;IAI/B,gBAAgB,IAAI,aAAa;IAIjC,eAAe,IAAI,YAAY;IAI/B,iBAAiB,IAAI,cAAc;IAInC,gBAAgB,IAAI,aAAa;IAIxC;;OAEG;IACI,eAAe,IAAI,OAAO;IAIjC;;OAEG;IACI,mBAAmB,IAAI,OAAO;CAGxC"}