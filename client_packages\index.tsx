/**
 * RAGE MP Advanced Roleplay Client
 * Main Client Entry Point
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 * @description Advanced roleplay client built with TypeScript and React
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import { ClientCore } from './core/ClientCore';
import { Logger } from './utils/Logger';
import { App } from './ui/App';

// Initialize client core
const client = new ClientCore();

// Initialize React UI
const initializeUI = (): void => {
    try {
        // Create root container for React
        const rootElement = document.createElement('div');
        rootElement.id = 'react-root';
        rootElement.style.position = 'fixed';
        rootElement.style.top = '0';
        rootElement.style.left = '0';
        rootElement.style.width = '100%';
        rootElement.style.height = '100%';
        rootElement.style.pointerEvents = 'none';
        rootElement.style.zIndex = '1000';
        
        document.body.appendChild(rootElement);

        // Create React root and render app
        const root = ReactDOM.createRoot(rootElement);
        root.render(<App />);

        Logger.info('🎨 React UI initialized successfully');
    } catch (error) {
        Logger.error('❌ Failed to initialize React UI:', error);
    }
};

// Start the client
const startClient = async (): Promise<void> => {
    try {
        Logger.info('🚀 Starting Advanced Roleplay Client...');

        // Initialize client core
        await client.start();

        // Initialize UI
        initializeUI();

        Logger.success('✅ Advanced Roleplay Client started successfully!');
    } catch (error) {
        Logger.error('❌ Failed to start client:', error);
    }
};

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startClient);
} else {
    startClient();
}

// Handle page unload
window.addEventListener('beforeunload', async () => {
    Logger.info('🛑 Shutting down client...');
    await client.shutdown();
});

// Global error handling
window.addEventListener('error', (event) => {
    Logger.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    Logger.error('Unhandled promise rejection:', event.reason);
});

// Export client instance for global access
(window as any).client = client;
