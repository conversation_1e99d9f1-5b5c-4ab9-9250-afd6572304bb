/**
 * RAGE Multiplayer Client-Side TypeScript Definitions
 * Enhanced type definitions for RAGE MP client-side development
 */

declare global {
    const mp: MpClient;

    interface MpClient {
        events: EventMpClient;
        players: PlayerPoolMpClient;
        vehicles: VehiclePoolMpClient;
        game: GameMpClient;
        gui: GuiMpClient;
        keys: KeysMpClient;
        discord: DiscordMpClient;
        storage: StorageMpClient;
        browsers: BrowserPoolMpClient;
        cameras: CameraPoolMpClient;
        colshapes: ColshapePoolMpClient;
        checkpoints: CheckpointPoolMpClient;
        markers: MarkerPoolMpClient;
        objects: ObjectPoolMpClient;
        pickups: PickupPoolMpClient;
        blips: BlipPoolMpClient;
        raycasting: RaycastingMpClient;
        nametags: NametagsMpClient;
        voice: VoiceMpClient;
    }

    interface EventMpClient {
        add(eventName: string, callback: (...args: any[]) => void): void;
        addDataChange(eventName: string, callback: (entity: any, value: any, oldValue: any) => void): void;
        call(eventName: string, ...args: any[]): void;
        callRemote(eventName: string, ...args: any[]): void;
        callRemoteUnreliable(eventName: string, ...args: any[]): void;
        remove(eventName: string, callback?: (...args: any[]) => void): void;
        removeAllOf(eventName: string): void;
    }

    interface PlayerMpClient {
        id: number;
        name: string;
        position: Vector3Mp;
        heading: number;
        health: number;
        armour: number;
        weapon: number;
        aimTarget: PlayerMpClient;
        vehicle: VehicleMpClient;
        seat: number;
        isLocal: boolean;
        
        // Methods
        getVariable(key: string): any;
        isInVehicle(): boolean;
        dist(position: Vector3Mp): number;
        distSquared(position: Vector3Mp): number;
    }

    interface PlayerPoolMpClient {
        length: number;
        size: number;
        local: PlayerMpClient;
        at(id: number): PlayerMpClient;
        exists(player: PlayerMpClient): boolean;
        forEach(callback: (player: PlayerMpClient) => void): void;
        toArray(): PlayerMpClient[];
    }

    interface VehicleMpClient {
        id: number;
        model: number;
        position: Vector3Mp;
        rotation: Vector3Mp;
        heading: number;
        velocity: Vector3Mp;
        engine: boolean;
        locked: boolean;
        
        // Methods
        getVariable(key: string): any;
        getOccupant(seat: number): PlayerMpClient;
        getOccupants(): PlayerMpClient[];
        isEngineOn(): boolean;
        isLocked(): boolean;
        dist(position: Vector3Mp): number;
        distSquared(position: Vector3Mp): number;
    }

    interface VehiclePoolMpClient {
        length: number;
        size: number;
        at(id: number): VehicleMpClient;
        exists(vehicle: VehicleMpClient): boolean;
        forEach(callback: (vehicle: VehicleMpClient) => void): void;
        toArray(): VehicleMpClient[];
    }

    interface GameMpClient {
        joaat(str: string): number;
        gameplay: {
            getGroundZFor3dCoord(x: number, y: number, z: number, groundZ: number, normal: boolean): number;
            getModelDimensions(modelHash: number): { min: Vector3Mp; max: Vector3Mp };
            findGameEntityPosition(entity: number, coords: Vector3Mp, heading: number): Vector3Mp;
            isModelValid(modelHash: number): boolean;
            requestModel(modelHash: number): void;
            hasModelLoaded(modelHash: number): boolean;
            setModelAsNoLongerNeeded(modelHash: number): void;
        };
        weapon: {
            getDamageModifierByHash(weaponHash: number): number;
            getWeaponDamageType(weaponHash: number): number;
        };
        streaming: {
            requestIpl(iplName: string): void;
            removeIpl(iplName: string): void;
            requestAnimDict(animDict: string): void;
            hasAnimDictLoaded(animDict: string): boolean;
            removeAnimDict(animDict: string): void;
        };
        ui: {
            getCursorPos(): Vector2Mp;
            setCursorPos(pos: Vector2Mp): void;
            isCursorShowing(): boolean;
            showCursor(toggle: boolean): void;
            isHudComponentActive(component: number): boolean;
            setHudComponentPosition(component: number, x: number, y: number): void;
        };
        graphics: {
            notify(message: string, blink?: boolean): void;
            subtitleText(text: string, duration: number, drawImmediately: boolean): void;
            drawText(text: string, position: Vector2Mp, options?: any): void;
            drawRect(position: Vector2Mp, size: Vector2Mp, color: RGBA): void;
            getScreenResolution(): Vector2Mp;
            getActiveScreenResolution(): Vector2Mp;
        };
        ped: {
            getLocalPlayer(): number;
        };
        player: {
            getLocalPlayer(): number;
            isPlayerFreeAiming(player: number): boolean;
            getPlayerTargetEntity(player: number): number;
        };
        vehicle: {
            getVehicleClass(vehicle: number): number;
            getVehicleMaxNumberOfPassengers(vehicle: number): number;
            setVehicleEngineOn(vehicle: number, value: boolean, instantly: boolean): void;
            setVehicleUndriveable(vehicle: number, toggle: boolean): void;
        };
        object: {
            createObject(modelHash: number, position: Vector3Mp, rotation: Vector3Mp, alpha: number, dimension: number): number;
            destroyObject(object: number): void;
        };
        cam: {
            createCam(camType: string, p1: boolean): number;
            createCamWithParams(camType: string, posX: number, posY: number, posZ: number, rotX: number, rotY: number, rotZ: number, fov: number, p8: boolean, p9: number): number;
            destroyCam(cam: number, thisScriptCheck: boolean): void;
            setCamActive(cam: number, active: boolean): void;
            renderScriptCams(render: boolean, ease: boolean, easeTime: number, p3: boolean, p4: boolean): void;
        };
    }

    interface GuiMpClient {
        cursor: {
            show(toggle: boolean, toggle2: boolean): void;
            visible: boolean;
            position: Vector2Mp;
        };
        chat: {
            activate(toggle: boolean): void;
            show(toggle: boolean): void;
            push(text: string): void;
            clear(): void;
            safeMode: boolean;
            colors: boolean;
        };
        execute(code: string): void;
    }

    interface KeysMpClient {
        bind(keyCode: number, keyDown: boolean, callback: () => void): void;
        unbind(keyCode: number, keyDown: boolean, callback?: () => void): void;
        unbindAll(): void;
        isDown(keyCode: number): boolean;
        isPressed(keyCode: number): boolean;
    }

    interface DiscordMpClient {
        update(appId: string, data: any): void;
    }

    interface StorageMpClient {
        data: { [key: string]: any };
        flush(): void;
    }

    interface BrowserMpClient {
        id: number;
        url: string;
        
        destroy(): void;
        execute(code: string): void;
        reload(ignoreCache: boolean): void;
        loadUrl(url: string): void;
        resize(width: number, height: number): void;
        focus(): void;
        unfocus(): void;
        markAsChat(): void;
    }

    interface BrowserPoolMpClient {
        length: number;
        size: number;
        new(url: string): BrowserMpClient;
        at(id: number): BrowserMpClient;
        exists(browser: BrowserMpClient): boolean;
        forEach(callback: (browser: BrowserMpClient) => void): void;
        toArray(): BrowserMpClient[];
    }

    interface CameraMpClient {
        id: number;
        position: Vector3Mp;
        rotation: Vector3Mp;
        direction: Vector3Mp;
        fov: number;
        
        destroy(): void;
        setActive(active: boolean): void;
        pointAt(position: Vector3Mp, entity?: any): void;
        attachTo(entity: any, offset: Vector3Mp, isRelative: boolean): void;
        detach(): void;
    }

    interface CameraPoolMpClient {
        length: number;
        size: number;
        new(name: string, position: Vector3Mp, rotation: Vector3Mp, fov: number): CameraMpClient;
        at(id: number): CameraMpClient;
        exists(camera: CameraMpClient): boolean;
        forEach(callback: (camera: CameraMpClient) => void): void;
        toArray(): CameraMpClient[];
    }

    interface Vector2Mp {
        x: number;
        y: number;
        new(x: number, y: number): Vector2Mp;
    }

    interface RGBA {
        r: number;
        g: number;
        b: number;
        a: number;
    }

    interface ColshapePoolMpClient {
        length: number;
        size: number;
        at(id: number): any;
        exists(colshape: any): boolean;
        forEach(callback: (colshape: any) => void): void;
        toArray(): any[];
    }

    interface CheckpointPoolMpClient {
        length: number;
        size: number;
        at(id: number): any;
        exists(checkpoint: any): boolean;
        forEach(callback: (checkpoint: any) => void): void;
        toArray(): any[];
    }

    interface MarkerPoolMpClient {
        length: number;
        size: number;
        at(id: number): any;
        exists(marker: any): boolean;
        forEach(callback: (marker: any) => void): void;
        toArray(): any[];
    }

    interface ObjectPoolMpClient {
        length: number;
        size: number;
        at(id: number): any;
        exists(object: any): boolean;
        forEach(callback: (object: any) => void): void;
        toArray(): any[];
    }

    interface PickupPoolMpClient {
        length: number;
        size: number;
        at(id: number): any;
        exists(pickup: any): boolean;
        forEach(callback: (pickup: any) => void): void;
        toArray(): any[];
    }

    interface BlipPoolMpClient {
        length: number;
        size: number;
        at(id: number): any;
        exists(blip: any): boolean;
        forEach(callback: (blip: any) => void): void;
        toArray(): any[];
    }

    interface RaycastingMpClient {
        testPointToPoint(startPos: Vector3Mp, endPos: Vector3Mp, entity?: any, flags?: number): any;
        testCapsule(startPos: Vector3Mp, endPos: Vector3Mp, radius: number, entity?: any, flags?: number): any;
    }

    interface NametagsMpClient {
        enabled: boolean;
        range: number;
    }

    interface VoiceMpClient {
        muted: boolean;
        range: number;
        listeners: PlayerMpClient[];
        
        enableFor(player: PlayerMpClient): void;
        disableFor(player: PlayerMpClient): void;
        isEnabledFor(player: PlayerMpClient): boolean;
    }
}
