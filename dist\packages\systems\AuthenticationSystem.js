"use strict";
/**
 * Authentication System
 * Handles user registration, login, and session management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationSystem = void 0;
const Logger_1 = require("../utils/Logger");
const DatabaseManager_1 = require("../database/DatabaseManager");
const EventManager_1 = require("../events/EventManager");
class AuthenticationSystem {
    constructor() {
        this.maxLoginAttempts = 5;
        this.lockoutDuration = 15 * 60 * 1000; // 15 minutes
        this.databaseManager = new DatabaseManager_1.DatabaseManager();
        this.eventManager = new EventManager_1.EventManager();
    }
    /**
     * Set dependencies (called by ServerCore)
     */
    setDependencies(databaseManager, eventManager) {
        this.databaseManager = databaseManager;
        this.eventManager = eventManager;
    }
    /**
     * Show login screen to player
     */
    async showLoginScreen(player) {
        try {
            Logger_1.Logger.info(`🔐 Showing login screen to ${player.name}`);
            // Check if player already has an account
            const existingUser = await this.checkExistingUser(player);
            if (existingUser) {
                // Show login form
                player.call('client:showLoginForm', [existingUser.username]);
            }
            else {
                // Show registration form
                player.call('client:showRegisterForm');
            }
            // Set player in authentication dimension
            player.dimension = 999;
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to show login screen for ${player.name}:`, error);
        }
    }
    /**
     * Handle player login attempt
     */
    async handleLogin(player, data) {
        try {
            Logger_1.Logger.info(`🔐 Login attempt from ${player.name}: ${data.username}`);
            // Validate input
            if (!this.validateLoginData(data)) {
                this.sendAuthError(player, 'Invalid login data');
                return;
            }
            // Check if player is locked out
            if (await this.isPlayerLockedOut(player)) {
                this.sendAuthError(player, 'Account temporarily locked due to too many failed attempts');
                return;
            }
            // Get user from database
            const user = await this.getUserByUsername(data.username);
            if (!user) {
                await this.handleFailedLogin(player, 'Invalid username or password');
                return;
            }
            // Verify password
            const isPasswordValid = await this.verifyPassword(data.password, user.password_hash);
            if (!isPasswordValid) {
                await this.handleFailedLogin(player, 'Invalid username or password');
                return;
            }
            // Check if user is banned
            if (user.is_banned) {
                this.sendAuthError(player, `Account banned: ${user.ban_reason || 'No reason specified'}`);
                return;
            }
            // Successful login
            await this.handleSuccessfulLogin(player, user);
        }
        catch (error) {
            Logger_1.Logger.error(`Login error for ${player.name}:`, error);
            this.sendAuthError(player, 'Login failed due to server error');
        }
    }
    /**
     * Handle player registration
     */
    async handleRegister(player, data) {
        try {
            Logger_1.Logger.info(`📝 Registration attempt from ${player.name}: ${data.username}`);
            // Validate input
            const validationError = this.validateRegisterData(data);
            if (validationError) {
                this.sendAuthError(player, validationError);
                return;
            }
            // Check if username already exists
            const existingUser = await this.getUserByUsername(data.username);
            if (existingUser) {
                this.sendAuthError(player, 'Username already exists');
                return;
            }
            // Check if email already exists
            const existingEmail = await this.getUserByEmail(data.email);
            if (existingEmail) {
                this.sendAuthError(player, 'Email already registered');
                return;
            }
            // Check if social club already exists
            const existingSocialClub = await this.getUserBySocialClub(player.socialClub);
            if (existingSocialClub) {
                this.sendAuthError(player, 'This Social Club account is already registered');
                return;
            }
            // Hash password
            const passwordHash = await this.hashPassword(data.password);
            // Create user account
            const userId = await this.createUserAccount(player, data, passwordHash);
            if (!userId) {
                this.sendAuthError(player, 'Failed to create account');
                return;
            }
            // Load user data
            const newUser = await this.getUserById(userId);
            if (!newUser) {
                this.sendAuthError(player, 'Failed to load account data');
                return;
            }
            // Successful registration and login
            await this.handleSuccessfulLogin(player, newUser);
            Logger_1.Logger.success(`✅ New user registered: ${data.username} (${player.name})`);
        }
        catch (error) {
            Logger_1.Logger.error(`Registration error for ${player.name}:`, error);
            this.sendAuthError(player, 'Registration failed due to server error');
        }
    }
    /**
     * Handle successful login
     */
    async handleSuccessfulLogin(player, user) {
        try {
            // Update player data
            player.userData = {
                id: user.id,
                username: user.username,
                email: user.email,
                socialClub: user.social_club,
                serial: user.serial,
                ipAddress: user.ip_address,
                registrationDate: user.registration_date,
                lastLogin: user.last_login,
                loginAttempts: 0,
                lockedUntil: null,
                isBanned: user.is_banned,
                banReason: user.ban_reason,
                adminLevel: user.admin_level,
                totalPlaytime: user.total_playtime
            };
            player.isLoggedIn = true;
            player.loginAttempts = 0;
            // Update database
            await this.databaseManager.execute('UPDATE users SET last_login = NOW(), login_attempts = 0, locked_until = NULL, ip_address = ? WHERE id = ?', [player.ip, user.id]);
            // Move to main dimension
            player.dimension = 0;
            // Show character selection
            await this.showCharacterSelection(player);
            // Emit login event
            await this.eventManager.emit('player:login', player);
            Logger_1.Logger.success(`✅ Player logged in: ${user.username} (${player.name})`);
        }
        catch (error) {
            Logger_1.Logger.error(`Error in successful login for ${player.name}:`, error);
        }
    }
    /**
     * Handle failed login
     */
    async handleFailedLogin(player, message) {
        player.loginAttempts++;
        // Update database
        const lockoutTime = player.loginAttempts >= this.maxLoginAttempts
            ? new Date(Date.now() + this.lockoutDuration)
            : null;
        if (player.userData?.id) {
            await this.databaseManager.execute('UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?', [player.loginAttempts, lockoutTime, player.userData.id]);
        }
        // Send error message
        if (player.loginAttempts >= this.maxLoginAttempts) {
            this.sendAuthError(player, 'Too many failed attempts. Account locked for 15 minutes.');
        }
        else {
            const attemptsLeft = this.maxLoginAttempts - player.loginAttempts;
            this.sendAuthError(player, `${message}. ${attemptsLeft} attempts remaining.`);
        }
        Logger_1.Logger.warn(`Failed login attempt: ${player.name} (${player.loginAttempts}/${this.maxLoginAttempts})`);
    }
    /**
     * Show character selection screen
     */
    async showCharacterSelection(player) {
        if (!player.userData)
            return;
        try {
            // Get player's characters
            const characters = await this.databaseManager.execute('SELECT * FROM characters WHERE user_id = ? AND is_active = TRUE', [player.userData.id]);
            // Send character data to client
            player.call('client:showCharacterSelection', [characters || []]);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to show character selection for ${player.name}:`, error);
        }
    }
    /**
     * Check if player already has an account
     */
    async checkExistingUser(player) {
        try {
            const result = await this.databaseManager.execute('SELECT username FROM users WHERE social_club = ? OR serial = ?', [player.socialClub, player.serial]);
            return result && result.length > 0 ? result[0] : null;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to check existing user:', error);
            return null;
        }
    }
    /**
     * Validate login data
     */
    validateLoginData(data) {
        return !!(data.username && data.password &&
            data.username.length >= 3 && data.username.length <= 50 &&
            data.password.length >= 6);
    }
    /**
     * Validate registration data
     */
    validateRegisterData(data) {
        if (!data.username || data.username.length < 3 || data.username.length > 50) {
            return 'Username must be between 3 and 50 characters';
        }
        if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
            return 'Username can only contain letters, numbers, and underscores';
        }
        if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            return 'Invalid email address';
        }
        if (!data.password || data.password.length < 6) {
            return 'Password must be at least 6 characters long';
        }
        if (data.password !== data.confirmPassword) {
            return 'Passwords do not match';
        }
        return null;
    }
    /**
     * Check if player is locked out
     */
    async isPlayerLockedOut(player) {
        if (!player.userData?.id)
            return false;
        try {
            const result = await this.databaseManager.execute('SELECT locked_until FROM users WHERE id = ?', [player.userData.id]);
            if (result && result.length > 0 && result[0].locked_until) {
                return new Date(result[0].locked_until) > new Date();
            }
            return false;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to check lockout status:', error);
            return false;
        }
    }
    /**
     * Get user by username
     */
    async getUserByUsername(username) {
        try {
            const result = await this.databaseManager.execute('SELECT * FROM users WHERE username = ?', [username]);
            return result && result.length > 0 ? result[0] : null;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to get user by username:', error);
            return null;
        }
    }
    /**
     * Get user by email
     */
    async getUserByEmail(email) {
        try {
            const result = await this.databaseManager.execute('SELECT * FROM users WHERE email = ?', [email]);
            return result && result.length > 0 ? result[0] : null;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to get user by email:', error);
            return null;
        }
    }
    /**
     * Get user by social club
     */
    async getUserBySocialClub(socialClub) {
        try {
            const result = await this.databaseManager.execute('SELECT * FROM users WHERE social_club = ?', [socialClub]);
            return result && result.length > 0 ? result[0] : null;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to get user by social club:', error);
            return null;
        }
    }
    /**
     * Get user by ID
     */
    async getUserById(id) {
        try {
            const result = await this.databaseManager.execute('SELECT * FROM users WHERE id = ?', [id]);
            return result && result.length > 0 ? result[0] : null;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to get user by ID:', error);
            return null;
        }
    }
    /**
     * Hash password
     */
    async hashPassword(password) {
        // TODO: Implement bcrypt hashing
        // For now, return a mock hash
        return `hashed_${password}`;
    }
    /**
     * Verify password
     */
    async verifyPassword(password, hash) {
        // TODO: Implement bcrypt verification
        // For now, simple comparison
        return hash === `hashed_${password}`;
    }
    /**
     * Create user account
     */
    async createUserAccount(player, data, passwordHash) {
        try {
            const result = await this.databaseManager.execute(`INSERT INTO users (username, email, password_hash, social_club, serial, ip_address, registration_date)
                 VALUES (?, ?, ?, ?, ?, ?, NOW())`, [data.username, data.email, passwordHash, player.socialClub, player.serial, player.ip]);
            return result && result.insertId ? result.insertId : null;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to create user account:', error);
            return null;
        }
    }
    /**
     * Send authentication error to player
     */
    sendAuthError(player, message) {
        player.call('client:authError', [message]);
        Logger_1.Logger.warn(`Auth error for ${player.name}: ${message}`);
    }
}
exports.AuthenticationSystem = AuthenticationSystem;
//# sourceMappingURL=AuthenticationSystem.js.map