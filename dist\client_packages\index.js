"use strict";
/**
 * RAGE MP Advanced Roleplay Client
 * Main Client Entry Point
 *
 * <AUTHOR> Name
 * @version 1.0.0
 * @description Advanced roleplay client built with TypeScript and React
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = __importDefault(require("react"));
const client_1 = __importDefault(require("react-dom/client"));
const ClientCore_1 = require("./core/ClientCore");
const Logger_1 = require("./utils/Logger");
const App_1 = require("./ui/App");
// Initialize client core
const client = new ClientCore_1.ClientCore();
// Initialize React UI
const initializeUI = () => {
    try {
        // Create root container for React
        const rootElement = document.createElement('div');
        rootElement.id = 'react-root';
        rootElement.style.position = 'fixed';
        rootElement.style.top = '0';
        rootElement.style.left = '0';
        rootElement.style.width = '100%';
        rootElement.style.height = '100%';
        rootElement.style.pointerEvents = 'none';
        rootElement.style.zIndex = '1000';
        document.body.appendChild(rootElement);
        // Create React root and render app
        const root = client_1.default.createRoot(rootElement);
        root.render(<App_1.App />);
        Logger_1.Logger.info('🎨 React UI initialized successfully');
    }
    catch (error) {
        Logger_1.Logger.error('❌ Failed to initialize React UI:', error);
    }
};
// Start the client
const startClient = async () => {
    try {
        Logger_1.Logger.info('🚀 Starting Advanced Roleplay Client...');
        // Initialize client core
        await client.start();
        // Initialize UI
        initializeUI();
        Logger_1.Logger.success('✅ Advanced Roleplay Client started successfully!');
    }
    catch (error) {
        Logger_1.Logger.error('❌ Failed to start client:', error);
    }
};
// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startClient);
}
else {
    startClient();
}
// Handle page unload
window.addEventListener('beforeunload', async () => {
    Logger_1.Logger.info('🛑 Shutting down client...');
    await client.shutdown();
});
// Global error handling
window.addEventListener('error', (event) => {
    Logger_1.Logger.error('Global error:', event.error);
});
window.addEventListener('unhandledrejection', (event) => {
    Logger_1.Logger.error('Unhandled promise rejection:', event.reason);
});
// Export client instance for global access
window.client = client;
//# sourceMappingURL=index.js.map