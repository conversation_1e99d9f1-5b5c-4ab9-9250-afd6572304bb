/**
 * Client-Side Logger System
 * Provides comprehensive logging functionality for client-side operations
 */

export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    SUCCESS = 4
}

export interface LogEntry {
    timestamp: Date;
    level: LogLevel;
    message: string;
    data?: any;
    source?: string;
}

export class Logger {
    private static logLevel: LogLevel = LogLevel.INFO;
    private static logs: LogEntry[] = [];
    private static maxLogs: number = 500; // Smaller limit for client
    private static enableConsole: boolean = true;

    /**
     * Set the minimum log level
     */
    public static setLogLevel(level: LogLevel): void {
        this.logLevel = level;
    }

    /**
     * Enable/disable console output
     */
    public static setConsoleOutput(enabled: boolean): void {
        this.enableConsole = enabled;
    }

    /**
     * Debug level logging
     */
    public static debug(message: string, data?: any, source?: string): void {
        this.log(LogLevel.DEBUG, message, data, source);
    }

    /**
     * Info level logging
     */
    public static info(message: string, data?: any, source?: string): void {
        this.log(LogLevel.INFO, message, data, source);
    }

    /**
     * Warning level logging
     */
    public static warn(message: string, data?: any, source?: string): void {
        this.log(LogLevel.WARN, message, data, source);
    }

    /**
     * Error level logging
     */
    public static error(message: string, data?: any, source?: string): void {
        this.log(LogLevel.ERROR, message, data, source);
    }

    /**
     * Success level logging
     */
    public static success(message: string, data?: any, source?: string): void {
        this.log(LogLevel.SUCCESS, message, data, source);
    }

    /**
     * Core logging method
     */
    private static log(level: LogLevel, message: string, data?: any, source?: string): void {
        if (level < this.logLevel) return;

        const timestamp = new Date();
        const logEntry: LogEntry = {
            timestamp,
            level,
            message,
            data,
            source
        };

        // Add to logs array
        this.logs.push(logEntry);

        // Maintain max logs limit
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }

        // Output to console if enabled
        if (this.enableConsole) {
            this.outputToConsole(logEntry);
        }

        // Send to server for persistent logging (optional)
        this.sendToServer(logEntry);
    }

    /**
     * Output log to browser console
     */
    private static outputToConsole(entry: LogEntry): void {
        const timestamp = entry.timestamp.toISOString().replace('T', ' ').substring(0, 19);
        const levelStr = this.getLevelString(entry.level);
        const sourceStr = entry.source ? ` [${entry.source}]` : '';
        
        let message = `[${timestamp}] ${levelStr}${sourceStr} ${entry.message}`;
        
        if (entry.data) {
            message += ` | Data:`;
        }

        // Use appropriate console method based on level
        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(message, entry.data || '');
                break;
            case LogLevel.INFO:
                console.info(message, entry.data || '');
                break;
            case LogLevel.WARN:
                console.warn(message, entry.data || '');
                break;
            case LogLevel.ERROR:
                console.error(message, entry.data || '');
                break;
            case LogLevel.SUCCESS:
                console.log(message, entry.data || '');
                break;
            default:
                console.log(message, entry.data || '');
        }
    }

    /**
     * Send log to server (optional)
     */
    private static sendToServer(entry: LogEntry): void {
        // Only send important logs to server to avoid spam
        if (entry.level >= LogLevel.WARN) {
            try {
                mp.events.callRemote('server:clientLog', JSON.stringify({
                    level: LogLevel[entry.level],
                    message: entry.message,
                    data: entry.data,
                    source: entry.source,
                    timestamp: entry.timestamp.toISOString()
                }));
            } catch (error) {
                // Silently fail if server communication is not available
            }
        }
    }

    /**
     * Get level string
     */
    private static getLevelString(level: LogLevel): string {
        switch (level) {
            case LogLevel.DEBUG:
                return '[DEBUG]';
            case LogLevel.INFO:
                return '[INFO]';
            case LogLevel.WARN:
                return '[WARN]';
            case LogLevel.ERROR:
                return '[ERROR]';
            case LogLevel.SUCCESS:
                return '[SUCCESS]';
            default:
                return '[UNKNOWN]';
        }
    }

    /**
     * Get all logs
     */
    public static getLogs(): LogEntry[] {
        return [...this.logs];
    }

    /**
     * Get logs by level
     */
    public static getLogsByLevel(level: LogLevel): LogEntry[] {
        return this.logs.filter(log => log.level === level);
    }

    /**
     * Clear all logs
     */
    public static clearLogs(): void {
        this.logs = [];
    }

    /**
     * Export logs to string
     */
    public static exportLogs(): string {
        return this.logs.map(log => {
            const timestamp = log.timestamp.toISOString().replace('T', ' ').substring(0, 19);
            const levelStr = this.getLevelString(log.level);
            const sourceStr = log.source ? ` [${log.source}]` : '';
            let message = `[${timestamp}] ${levelStr}${sourceStr} ${log.message}`;
            
            if (log.data) {
                message += ` | Data: ${JSON.stringify(log.data)}`;
            }
            
            return message;
        }).join('\n');
    }

    /**
     * Get logs for UI display
     */
    public static getLogsForUI(limit: number = 100): LogEntry[] {
        return this.logs.slice(-limit);
    }

    /**
     * Filter logs by search term
     */
    public static searchLogs(searchTerm: string): LogEntry[] {
        const term = searchTerm.toLowerCase();
        return this.logs.filter(log => 
            log.message.toLowerCase().includes(term) ||
            (log.source && log.source.toLowerCase().includes(term)) ||
            (log.data && JSON.stringify(log.data).toLowerCase().includes(term))
        );
    }

    /**
     * Get log statistics
     */
    public static getLogStats(): { [key: string]: number } {
        const stats: { [key: string]: number } = {
            total: this.logs.length,
            debug: 0,
            info: 0,
            warn: 0,
            error: 0,
            success: 0
        };

        this.logs.forEach(log => {
            switch (log.level) {
                case LogLevel.DEBUG:
                    stats.debug++;
                    break;
                case LogLevel.INFO:
                    stats.info++;
                    break;
                case LogLevel.WARN:
                    stats.warn++;
                    break;
                case LogLevel.ERROR:
                    stats.error++;
                    break;
                case LogLevel.SUCCESS:
                    stats.success++;
                    break;
            }
        });

        return stats;
    }
}
