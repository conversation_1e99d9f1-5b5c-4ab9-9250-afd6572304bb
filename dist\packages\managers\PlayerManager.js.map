{"version": 3, "file": "PlayerManager.js", "sourceRoot": "", "sources": ["../../../packages/managers/PlayerManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AACzC,iEAA8D;AAC9D,yDAAsD;AAyDtD,MAAa,aAAa;IAKtB;QAJQ,YAAO,GAAgC,IAAI,GAAG,EAAE,CAAC;QAKrD,uCAAuC;QACvC,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,eAAgC,EAAE,YAA0B;QAC/E,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,MAAgB;QACtC,IAAI,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAE5C,eAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,UAAU,iBAAiB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAExF,iCAAiC;YACjC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAE1C,yBAAyB;YACzB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;YAE5D,6BAA6B;YAC7B,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QAEzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,MAAgB;QACtC,IAAI,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACnD,IAAI,CAAC,cAAc;gBAAE,OAAO;YAE5B,eAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,kBAAkB,CAAC,CAAC;YAExD,mBAAmB;YACnB,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC9C,CAAC;YAED,kBAAkB;YAClB,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAE1C,yBAAyB;YACzB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;YAE5D,6BAA6B;YAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAEnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,QAAgB;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,IAAY;QAC/B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACzC,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBACnD,OAAO,MAAM,CAAC;YAClB,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACvB,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE5C,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACxD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CACtC,eAAM,CAAC,KAAK,CAAC,2BAA2B,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CACjE,CACJ,CAAC;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAChC,eAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,MAAsB;QAC/C,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC/C,yDAAyD,EACzD,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,CACrC,CAAC;YAEF,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAe,CAAC;gBAC5C,eAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,eAAM,CAAC,KAAK,CAAC,0BAA0B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1D,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,MAAsB;QAC/C,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,OAAO;QAEtD,IAAI,CAAC;YACD,iBAAiB;YACjB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC9B;;;;8BAIc,EACd,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CACjE,CAAC;YAEF,sBAAsB;YACtB,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC9B;;;;;;;kCAOc,EACd;oBACI,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,aAAa,CAAC,SAAS;oBAC1D,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO;oBACvE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,aAAa,CAAC,QAAQ;oBAC/C,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM;oBAC5B,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,MAAM;oBACrF,MAAM,CAAC,aAAa,CAAC,QAAQ;oBAC7B,MAAM,CAAC,aAAa,CAAC,EAAE;iBAC1B,CACJ,CAAC;YACN,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,yBAAyB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,MAAsB;QAC/C,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAAE,OAAO;QAErC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACnE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;QAEvD,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,CAAC,QAAQ,CAAC,aAAa,IAAI,cAAc,CAAC;QACpD,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,CAAC,aAAa,CAAC,QAAQ,IAAI,cAAc,CAAC;QACpD,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,IAAI,MAAM,cAAc,UAAU,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,MAAgB;QACjC,MAAM,cAAc,GAAG,MAAwB,CAAC;QAEhD,cAAc,CAAC,UAAU,GAAG,KAAK,CAAC;QAClC,cAAc,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAC3C,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;QACjC,cAAc,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QACzC,cAAc,CAAC,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7C,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAAsB;QAC1C,sCAAsC;QACtC,MAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxD,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;QAErB,qCAAqC;QACrC,yEAAyE;IAC7E,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,MAAsB,EAAE,SAAiB,qBAAqB;QAC5E,eAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS,CAAC,MAAsB,EAAE,SAAiB,qBAAqB,EAAE,YAAoB,QAAQ;QAC/G,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;YAE3D,kBAAkB;YAClB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAC9B,gEAAgE,EAChE,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAC/B,CAAC;YACN,CAAC;YAED,iBAAiB;YACjB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAEzE,cAAc;YACd,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,wBAAwB,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAsB,EAAE,OAAe,EAAE,OAAe,MAAM;QACxE,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,OAAe,EAAE,OAAe,MAAM;QACnD,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,oBAAoB;QACvB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC;IAC5C,CAAC;CACJ;AAjTD,sCAiTC"}