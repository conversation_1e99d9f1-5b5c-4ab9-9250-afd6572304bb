{"version": 3, "file": "UIManager.js", "sourceRoot": "", "sources": ["../../../client_packages/ui/UIManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AAqBzC,MAAa,SAAS;IAKlB,YAAY,YAA0B;QAF9B,wBAAmB,GAAW,CAAC,CAAC;QAGpC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG;YACX,SAAS,EAAE,IAAI;YACf,aAAa,EAAE,SAAS;YACxB,aAAa,EAAE,EAAE;YACjB,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,IAAI;YACjB,aAAa,EAAE,KAAK;SACvB,CAAC;IACN,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACnB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAE7C,qBAAqB;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;YAEtB,0BAA0B;YAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAE7B,eAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ;QACjB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAE9C,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,cAAc;YACd,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAE7B,eAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACT,iCAAiC;QACjC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,qBAAqB;QACrB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAElE,eAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,OAAe,EAAE,OAAiC,MAAM,EAAE,WAAmB,IAAI;QACrG,MAAM,YAAY,GAAqB;YACnC,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAChD,OAAO;YACP,IAAI;YACJ,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,2BAA2B;QAC3B,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;QAEnD,eAAM,CAAC,KAAK,CAAC,uBAAuB,OAAO,KAAK,IAAI,GAAG,CAAC,CAAC;QAEzD,6BAA6B;QAC7B,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACf,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC3C,CAAC,EAAE,QAAQ,CAAC,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,cAAsB;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC;QACjF,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;YACtD,eAAM,CAAC,KAAK,CAAC,wBAAwB,cAAc,EAAE,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QACvC,eAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,QAAiB;QAClC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC;QACrC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QACjD,eAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,UAAU,CAAC;QACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;QACtC,eAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAe;QAChC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,UAAiB;QAC3C,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,qBAAqB,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QAC5D,eAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAAe;QACrC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,aAAkB;QACvC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,aAAa,CAAC,IAAI,GAAG,EAAE,SAAS,CAAC,CAAC;QACzE,eAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QACnD,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;QACrE,eAAM,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAgB;QACjC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAChD,eAAM,CAAC,KAAK,CAAC,0BAA0B,OAAO,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,UAAU;QACb,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QACrD,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QACvE,eAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC;QAErC,IAAI,OAAO,EAAE,KAAK,WAAW,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YACvD,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QACnD,eAAM,CAAC,KAAK,CAAC,6BAA6B,OAAO,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,SAAiB,EAAE,IAAU;QAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACpD,eAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,EAAE,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,SAAiB;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAC/C,eAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,EAAE,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;QACnC,eAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAA0D;QACjF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAgC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9F,CAAC;IAEO,kBAAkB,CAAC,IAAoB;QAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;IAEO,WAAW;QACf,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAEO,WAAW,CAAC,IAA0B;QAC1C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,SAAiB,EAAE,IAAU;QAC7C,gCAAgC;QAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;QAEhD,qDAAqD;QACrD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,MAAM,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/E,CAAC;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;YAC1E,IAAI,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACrD,MAAM,OAAO,GAAG,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACvD,OAAO,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC;YAC3C,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,UAAU;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,SAAiB;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;CACJ;AA9UD,8BA8UC"}