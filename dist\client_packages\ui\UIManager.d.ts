/**
 * UI Manager
 * Manages all UI interactions and state
 */
import { EventManager } from '../events/EventManager';
export interface NotificationData {
    id: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    duration?: number;
    timestamp: Date;
}
export interface UIState {
    isVisible: boolean;
    currentScreen: string;
    notifications: NotificationData[];
    modals: string[];
    hudVisible: boolean;
    chatVisible: boolean;
    cursorVisible: boolean;
}
export declare class UIManager {
    private eventManager;
    private uiState;
    private notificationCounter;
    constructor(eventManager: EventManager);
    /**
     * Initialize UI Manager
     */
    initialize(): Promise<void>;
    /**
     * Shutdown UI Manager
     */
    shutdown(): Promise<void>;
    /**
     * Update UI (called every frame)
     */
    update(): void;
    /**
     * Register UI events
     */
    private registerEvents;
    /**
     * Show notification
     */
    showNotification(message: string, type?: NotificationData['type'], duration?: number): void;
    /**
     * Hide notification
     */
    hideNotification(notificationId: string): void;
    /**
     * Clear all notifications
     */
    clearAllNotifications(): void;
    /**
     * Show login form
     */
    showLoginForm(username?: string): void;
    /**
     * Show register form
     */
    showRegisterForm(): void;
    /**
     * Show authentication error
     */
    showAuthError(message: string): void;
    /**
     * Show character selection
     */
    showCharacterSelection(characters: any[]): void;
    /**
     * Show character error
     */
    showCharacterError(message: string): void;
    /**
     * Handle character loaded
     */
    onCharacterLoaded(characterData: any): void;
    /**
     * Toggle HUD visibility
     */
    toggleHUD(): void;
    /**
     * Set HUD visibility
     */
    setHUDVisible(visible: boolean): void;
    /**
     * Toggle chat visibility
     */
    toggleChat(): void;
    /**
     * Set cursor visibility
     */
    setCursorVisible(visible: boolean): void;
    /**
     * Open modal
     */
    openModal(modalName: string, data?: any): void;
    /**
     * Close modal
     */
    closeModal(modalName: string): void;
    /**
     * Close all modals
     */
    closeAllModals(): void;
    /**
     * Event handlers
     */
    private onShowNotification;
    private onHideNotification;
    private onToggleHUD;
    private onToggleChat;
    private onSetCursor;
    /**
     * Emit UI event to React components
     */
    private emitUIEvent;
    /**
     * Clean up expired notifications
     */
    private cleanupNotifications;
    /**
     * Get current UI state
     */
    getUIState(): UIState;
    /**
     * Get notifications
     */
    getNotifications(): NotificationData[];
    /**
     * Check if modal is open
     */
    isModalOpen(modalName: string): boolean;
    /**
     * Get open modals
     */
    getOpenModals(): string[];
}
//# sourceMappingURL=UIManager.d.ts.map