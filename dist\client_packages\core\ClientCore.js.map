{"version": 3, "file": "ClientCore.js", "sourceRoot": "", "sources": ["../../../client_packages/core/ClientCore.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AACzC,yDAAsD;AACtD,+CAA4C;AAC5C,wDAAqD;AACrD,2DAAwD;AACxD,wDAAqD;AACrD,8DAA2D;AAC3D,2DAAwD;AAExD,MAAa,UAAU;IAcnB;QAZQ,kBAAa,GAAY,KAAK,CAAC;QAC/B,cAAS,GAAY,KAAK,CAAC;QAY/B,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO,UAAU,CAAC,QAAQ,CAAC;QAC/B,CAAC;QACD,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,KAAK;QACd,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAE9C,2BAA2B;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAEhC,0BAA0B;YAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;YAEvC,6BAA6B;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE5D,sBAAsB;YACtB,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClD,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxD,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAExD,sBAAsB;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;YAEtB,0BAA0B;YAC1B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,eAAM,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAE/C,IAAI,CAAC;YACD,wBAAwB;YACxB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,eAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC5B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAClC,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAErC,eAAM,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC1B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAErC,eAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,iBAAiB;QACjB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClD,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE9E,uBAAuB;QACvB,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7E,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/D,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzF,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3E,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAErE,eAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,QAAQ;QACZ,6BAA6B;QAC7B,8CAA8C;QAC9C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAChC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAkB;QACvC,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAkB,EAAE,GAAW,EAAE,SAAiB,EAAE,SAAiB;QAChG,eAAM,CAAC,KAAK,CAAC,2BAA2B,GAAG,KAAK,SAAS,KAAK,SAAS,GAAG,CAAC,CAAC;QAC5E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe,EAAE,OAAe,MAAM;QACzD,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAiB;QACrC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,kBAAkB;QACtB,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,OAAe;QAC/B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,UAAiB;QAC9C,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,aAAkB;QACxC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe;QACpC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAa;QACjC,2CAA2C;QAC3C,0CAA0C;QAC1C,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,WAAgB;QACnC,gCAAgC;QAChC,0CAA0C;QAC1C,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;IACnD,CAAC;IAED,iCAAiC;IAC1B,MAAM,CAAC,WAAW;QACrB,OAAO,UAAU,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAEM,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEM,YAAY;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAEM,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEM,gBAAgB;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEM,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEM,iBAAiB;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEM,gBAAgB;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;CACJ;AAvRD,gCAuRC"}