{"version": 3, "file": "DatabaseManager.js", "sourceRoot": "", "sources": ["../../../packages/database/DatabaseManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,4CAAyC;AACzC,2DAAwD;AAcxD,MAAa,eAAe;IAKxB;QAJQ,SAAI,GAAgB,IAAI,CAAC;QAEzB,gBAAW,GAAY,KAAK,CAAC;QAGjC,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO;QAChB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAE5C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEpD,2CAA2C;YAC3C,uCAAuC;YACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAElC,kBAAkB;YAClB,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAE5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,eAAM,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YAEpD,6BAA6B;YAC7B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAElC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACnB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO;QAE5C,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,eAAM,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QACxB,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAEjE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC3D,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC1B,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAElD,MAAM,MAAM,GAAG;YACX,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,cAAc,EAAE;SACxB,CAAC;QAEF,KAAK,MAAM,UAAU,IAAI,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;YACvD,CAAC;QACL,CAAC;QAED,eAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,KAAa,EAAE,SAAgB,EAAE;QAClD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YACvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACtD,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,gBAAgB;QACpB,OAAO;;;;;;;;;;;;;;;;;;;;SAoBN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,qBAAqB;QACzB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAmCN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,mBAAmB;QACvB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA8BN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,qBAAqB;QACzB,OAAO;;;;;;;;;;;;;;;;;;;;;SAqBN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,qBAAqB;QACzB,OAAO;;;;;;;;;;;;;;;;;;;;SAoBN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,OAAO;;;;;;;;;;;;;;SAcN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC3B,OAAO;;;;;;;;;;;;;;;SAeN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,OAAO;;;;;;;;;;;;;SAaN,CAAC;IACN,CAAC;IAED;;;OAGG;IACK,cAAc;QAClB,OAAO;YACH,KAAK,CAAC,aAAa;gBACf,OAAO;oBACH,KAAK,CAAC,OAAO,CAAC,KAAa,EAAE,MAAc;wBACvC,sBAAsB;wBACtB,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;oBACzB,CAAC;oBACD,KAAK,CAAC,GAAG;wBACL,sBAAsB;oBAC1B,CAAC;iBACJ,CAAC;YACN,CAAC;YACD,KAAK,CAAC,OAAO,CAAC,KAAa,EAAE,MAAc;gBACvC,sBAAsB;gBACtB,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;YACzB,CAAC;YACD,KAAK,CAAC,GAAG;gBACL,sBAAsB;YAC1B,CAAC;SACJ,CAAC;IACN,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;CACJ;AA5XD,0CA4XC"}