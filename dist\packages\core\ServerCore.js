"use strict";
/**
 * Server Core - Main server management class
 * Handles initialization, module loading, and server lifecycle
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerCore = void 0;
const Logger_1 = require("../utils/Logger");
const ConfigManager_1 = require("../config/ConfigManager");
const DatabaseManager_1 = require("../database/DatabaseManager");
const EventManager_1 = require("../events/EventManager");
const PlayerManager_1 = require("../managers/PlayerManager");
const VehicleManager_1 = require("../managers/VehicleManager");
const AuthenticationSystem_1 = require("../systems/AuthenticationSystem");
const CharacterSystem_1 = require("../systems/CharacterSystem");
class ServerCore {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        if (ServerCore.instance) {
            return ServerCore.instance;
        }
        ServerCore.instance = this;
    }
    /**
     * Initialize all server components
     */
    async start() {
        try {
            Logger_1.Logger.info('🔧 Initializing server core...');
            // Initialize configuration
            this.configManager = new ConfigManager_1.ConfigManager();
            await this.configManager.load();
            // Initialize database
            this.databaseManager = new DatabaseManager_1.DatabaseManager();
            await this.databaseManager.connect();
            // Initialize event system
            this.eventManager = new EventManager_1.EventManager();
            // Initialize managers
            this.playerManager = new PlayerManager_1.PlayerManager();
            this.vehicleManager = new VehicleManager_1.VehicleManager();
            // Initialize systems
            this.authSystem = new AuthenticationSystem_1.AuthenticationSystem();
            this.characterSystem = new CharacterSystem_1.CharacterSystem();
            // Register all events
            this.registerEvents();
            this.isInitialized = true;
            this.isRunning = true;
            Logger_1.Logger.success('✅ Server core initialized successfully');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Failed to initialize server core:', error);
            throw error;
        }
    }
    /**
     * Shutdown server gracefully
     */
    async shutdown() {
        if (!this.isRunning)
            return;
        Logger_1.Logger.info('🛑 Shutting down server core...');
        try {
            // Save all player data
            await this.playerManager.saveAllPlayers();
            // Close database connections
            await this.databaseManager.disconnect();
            this.isRunning = false;
            Logger_1.Logger.success('✅ Server shutdown completed');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Error during shutdown:', error);
        }
    }
    /**
     * Register all server events
     */
    registerEvents() {
        // Player events
        mp.events.add('playerJoin', this.onPlayerJoin.bind(this));
        mp.events.add('playerQuit', this.onPlayerQuit.bind(this));
        mp.events.add('playerReady', this.onPlayerReady.bind(this));
        mp.events.add('playerDeath', this.onPlayerDeath.bind(this));
        // Custom events
        mp.events.add('server:playerLogin', this.onPlayerLogin.bind(this));
        mp.events.add('server:playerRegister', this.onPlayerRegister.bind(this));
    }
    /**
     * Handle player join
     */
    async onPlayerJoin(player) {
        Logger_1.Logger.info(`👤 Player ${player.name} joined the server`);
        await this.playerManager.onPlayerJoin(player);
    }
    /**
     * Handle player quit
     */
    async onPlayerQuit(player, exitType, reason) {
        Logger_1.Logger.info(`👋 Player ${player.name} left the server (${exitType}: ${reason})`);
        await this.playerManager.onPlayerQuit(player);
    }
    /**
     * Handle player ready
     */
    async onPlayerReady(player) {
        Logger_1.Logger.info(`✅ Player ${player.name} is ready`);
        await this.authSystem.showLoginScreen(player);
    }
    /**
     * Handle player death
     */
    async onPlayerDeath(player, reason, killer) {
        Logger_1.Logger.info(`💀 Player ${player.name} died`);
        // Handle death logic here
    }
    /**
     * Handle player login
     */
    async onPlayerLogin(player, data) {
        await this.authSystem.handleLogin(player, data);
    }
    /**
     * Handle player registration
     */
    async onPlayerRegister(player, data) {
        await this.authSystem.handleRegister(player, data);
    }
    // Getters for accessing managers and systems
    static getInstance() {
        return ServerCore.instance;
    }
    getConfigManager() {
        return this.configManager;
    }
    getDatabaseManager() {
        return this.databaseManager;
    }
    getEventManager() {
        return this.eventManager;
    }
    getPlayerManager() {
        return this.playerManager;
    }
    getVehicleManager() {
        return this.vehicleManager;
    }
    getAuthSystem() {
        return this.authSystem;
    }
    getCharacterSystem() {
        return this.characterSystem;
    }
}
exports.ServerCore = ServerCore;
//# sourceMappingURL=ServerCore.js.map