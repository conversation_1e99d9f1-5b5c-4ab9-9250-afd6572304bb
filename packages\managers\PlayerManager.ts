/**
 * Player Manager
 * Handles all player-related operations and data management
 */

import { Logger } from '../utils/Logger';
import { DatabaseManager } from '../database/DatabaseManager';
import { EventManager } from '../events/EventManager';

export interface PlayerData {
    id: number;
    username: string;
    email: string;
    socialClub: string;
    serial: string;
    ipAddress: string;
    registrationDate: Date;
    lastLogin: Date | null;
    loginAttempts: number;
    lockedUntil: Date | null;
    isBanned: boolean;
    banReason: string | null;
    adminLevel: number;
    totalPlaytime: number;
}

export interface CharacterData {
    id: number;
    userId: number;
    name: string;
    age: number;
    gender: 'male' | 'female';
    money: number;
    bankMoney: number;
    position: { x: number; y: number; z: number };
    heading: number;
    dimension: number;
    interior: number;
    health: number;
    armor: number;
    hunger: number;
    thirst: number;
    stress: number;
    job: string;
    jobRank: number;
    jobSalary: number;
    faction: string | null;
    factionRank: number;
    skinData: any;
    clothesData: any;
    isActive: boolean;
    playtime: number;
}

export interface ExtendedPlayer extends PlayerMp {
    userData?: PlayerData;
    characterData?: CharacterData;
    isLoggedIn: boolean;
    isCharacterSelected: boolean;
    loginAttempts: number;
    lastActivity: Date;
    sessionStartTime: Date;
}

export class PlayerManager {
    private players: Map<number, ExtendedPlayer> = new Map();
    private databaseManager: DatabaseManager;
    private eventManager: EventManager;

    constructor() {
        // These will be injected by ServerCore
        this.databaseManager = new DatabaseManager();
        this.eventManager = new EventManager();
    }

    /**
     * Set dependencies (called by ServerCore)
     */
    public setDependencies(databaseManager: DatabaseManager, eventManager: EventManager): void {
        this.databaseManager = databaseManager;
        this.eventManager = eventManager;
    }

    /**
     * Handle player join
     */
    public async onPlayerJoin(player: PlayerMp): Promise<void> {
        try {
            const extendedPlayer = this.extendPlayer(player);
            this.players.set(player.id, extendedPlayer);

            Logger.info(`👤 Player ${player.name} (${player.socialClub}) joined from ${player.ip}`);

            // Load player data from database
            await this.loadPlayerData(extendedPlayer);

            // Emit player join event
            await this.eventManager.emit('player:join', extendedPlayer);

            // Set initial spawn position
            this.setInitialSpawn(extendedPlayer);

        } catch (error) {
            Logger.error(`Error handling player join for ${player.name}:`, error);
        }
    }

    /**
     * Handle player quit
     */
    public async onPlayerQuit(player: PlayerMp): Promise<void> {
        try {
            const extendedPlayer = this.players.get(player.id);
            if (!extendedPlayer) return;

            Logger.info(`👋 Player ${player.name} left the server`);

            // Save player data
            if (extendedPlayer.isLoggedIn) {
                await this.savePlayerData(extendedPlayer);
            }

            // Update playtime
            await this.updatePlaytime(extendedPlayer);

            // Emit player quit event
            await this.eventManager.emit('player:quit', extendedPlayer);

            // Remove from active players
            this.players.delete(player.id);

        } catch (error) {
            Logger.error(`Error handling player quit for ${player.name}:`, error);
        }
    }

    /**
     * Get extended player by ID
     */
    public getPlayer(playerId: number): ExtendedPlayer | undefined {
        return this.players.get(playerId);
    }

    /**
     * Get extended player by name
     */
    public getPlayerByName(name: string): ExtendedPlayer | undefined {
        for (const player of this.players.values()) {
            if (player.name.toLowerCase() === name.toLowerCase()) {
                return player;
            }
        }
        return undefined;
    }

    /**
     * Get all online players
     */
    public getAllPlayers(): ExtendedPlayer[] {
        return Array.from(this.players.values());
    }

    /**
     * Get logged in players
     */
    public getLoggedInPlayers(): ExtendedPlayer[] {
        return this.getAllPlayers().filter(player => player.isLoggedIn);
    }

    /**
     * Get players with character selected
     */
    public getPlayersInGame(): ExtendedPlayer[] {
        return this.getAllPlayers().filter(player => player.isCharacterSelected);
    }

    /**
     * Save all player data
     */
    public async saveAllPlayers(): Promise<void> {
        Logger.info('💾 Saving all player data...');
        
        const savePromises = this.getLoggedInPlayers().map(player => 
            this.savePlayerData(player).catch(error => 
                Logger.error(`Failed to save data for ${player.name}:`, error)
            )
        );

        await Promise.all(savePromises);
        Logger.success('✅ All player data saved');
    }

    /**
     * Load player data from database
     */
    private async loadPlayerData(player: ExtendedPlayer): Promise<void> {
        try {
            const userData = await this.databaseManager.execute(
                'SELECT * FROM users WHERE social_club = ? OR serial = ?',
                [player.socialClub, player.serial]
            );

            if (userData && userData.length > 0) {
                player.userData = userData[0] as PlayerData;
                Logger.debug(`Loaded user data for ${player.name}`);
            } else {
                Logger.debug(`No user data found for ${player.name}`);
            }
        } catch (error) {
            Logger.error(`Failed to load player data for ${player.name}:`, error);
        }
    }

    /**
     * Save player data to database
     */
    private async savePlayerData(player: ExtendedPlayer): Promise<void> {
        if (!player.userData || !player.characterData) return;

        try {
            // Save user data
            await this.databaseManager.execute(
                `UPDATE users SET 
                 last_login = NOW(), 
                 total_playtime = ?,
                 ip_address = ?
                 WHERE id = ?`,
                [player.userData.totalPlaytime, player.ip, player.userData.id]
            );

            // Save character data
            if (player.characterData) {
                await this.databaseManager.execute(
                    `UPDATE characters SET 
                     money = ?, bank_money = ?, 
                     position_x = ?, position_y = ?, position_z = ?, heading = ?,
                     dimension = ?, interior = ?,
                     health = ?, armor = ?,
                     hunger = ?, thirst = ?, stress = ?,
                     playtime = ?
                     WHERE id = ?`,
                    [
                        player.characterData.money, player.characterData.bankMoney,
                        player.position.x, player.position.y, player.position.z, player.heading,
                        player.dimension, player.characterData.interior,
                        player.health, player.armour,
                        player.characterData.hunger, player.characterData.thirst, player.characterData.stress,
                        player.characterData.playtime,
                        player.characterData.id
                    ]
                );
            }

            Logger.debug(`Saved player data for ${player.name}`);
        } catch (error) {
            Logger.error(`Failed to save player data for ${player.name}:`, error);
        }
    }

    /**
     * Update player playtime
     */
    private async updatePlaytime(player: ExtendedPlayer): Promise<void> {
        if (!player.sessionStartTime) return;

        const sessionTime = Date.now() - player.sessionStartTime.getTime();
        const sessionMinutes = Math.floor(sessionTime / 60000);

        if (player.userData) {
            player.userData.totalPlaytime += sessionMinutes;
        }

        if (player.characterData) {
            player.characterData.playtime += sessionMinutes;
        }

        Logger.debug(`Updated playtime for ${player.name}: +${sessionMinutes} minutes`);
    }

    /**
     * Extend player object with additional properties
     */
    private extendPlayer(player: PlayerMp): ExtendedPlayer {
        const extendedPlayer = player as ExtendedPlayer;
        
        extendedPlayer.isLoggedIn = false;
        extendedPlayer.isCharacterSelected = false;
        extendedPlayer.loginAttempts = 0;
        extendedPlayer.lastActivity = new Date();
        extendedPlayer.sessionStartTime = new Date();

        return extendedPlayer;
    }

    /**
     * Set initial spawn position
     */
    private setInitialSpawn(player: ExtendedPlayer): void {
        // Spawn at default location initially
        const spawnPos = new mp.Vector3(-1037.8, -2738.5, 20.2);
        player.spawn(spawnPos, 0);
        player.dimension = 0;
        
        // Freeze player until authentication
        // player.freezePosition(true); // This method might not exist in RAGE MP
    }

    /**
     * Kick player with reason
     */
    public kickPlayer(player: ExtendedPlayer, reason: string = 'No reason specified'): void {
        Logger.info(`🦵 Kicking player ${player.name}: ${reason}`);
        player.kick(reason);
    }

    /**
     * Ban player with reason
     */
    public async banPlayer(player: ExtendedPlayer, reason: string = 'No reason specified', adminName: string = 'System'): Promise<void> {
        try {
            Logger.info(`🔨 Banning player ${player.name}: ${reason}`);

            // Update database
            if (player.userData) {
                await this.databaseManager.execute(
                    'UPDATE users SET is_banned = TRUE, ban_reason = ? WHERE id = ?',
                    [reason, player.userData.id]
                );
            }

            // Emit ban event
            await this.eventManager.emit('player:banned', player, reason, adminName);

            // Kick player
            player.ban(reason);
        } catch (error) {
            Logger.error(`Failed to ban player ${player.name}:`, error);
        }
    }

    /**
     * Send notification to player
     */
    public notify(player: ExtendedPlayer, message: string, type: string = 'info'): void {
        player.call('client:notification', [message, type]);
    }

    /**
     * Send notification to all players
     */
    public notifyAll(message: string, type: string = 'info'): void {
        this.getAllPlayers().forEach(player => {
            this.notify(player, message, type);
        });
    }

    /**
     * Get player count
     */
    public getPlayerCount(): number {
        return this.players.size;
    }

    /**
     * Get online player count
     */
    public getOnlinePlayerCount(): number {
        return this.getLoggedInPlayers().length;
    }
}
