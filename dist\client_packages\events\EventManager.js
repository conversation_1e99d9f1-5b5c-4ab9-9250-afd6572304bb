"use strict";
/**
 * Client-Side Event Manager
 * Advanced event handling system for client-side operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NamespacedEventManager = exports.EventManager = void 0;
const Logger_1 = require("../utils/Logger");
class EventManager {
    constructor() {
        this.events = new Map();
        this.middlewares = [];
        this.eventHistory = [];
        this.maxHistorySize = 500; // Smaller for client
    }
    /**
     * Add an event listener
     */
    on(eventName, callback, priority = 0) {
        this.addEventListener(eventName, callback, false, priority);
    }
    /**
     * Add a one-time event listener
     */
    once(eventName, callback, priority = 0) {
        this.addEventListener(eventName, callback, true, priority);
    }
    /**
     * Remove an event listener
     */
    off(eventName, callback) {
        const listeners = this.events.get(eventName);
        if (!listeners)
            return;
        if (callback) {
            const index = listeners.findIndex(listener => listener.callback === callback);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
        else {
            this.events.delete(eventName);
        }
        Logger_1.Logger.debug(`Event listener removed: ${eventName}`);
    }
    /**
     * Remove all listeners for an event
     */
    removeAllListeners(eventName) {
        if (eventName) {
            this.events.delete(eventName);
            Logger_1.Logger.debug(`All listeners removed for event: ${eventName}`);
        }
        else {
            this.events.clear();
            Logger_1.Logger.debug('All event listeners removed');
        }
    }
    /**
     * Emit an event
     */
    async emit(eventName, ...args) {
        try {
            // Add to history
            this.addToHistory(eventName, args);
            // Apply middlewares
            await this.applyMiddlewares(eventName, args);
            // Get listeners
            const listeners = this.events.get(eventName);
            if (!listeners || listeners.length === 0) {
                Logger_1.Logger.debug(`No listeners for event: ${eventName}`);
                return;
            }
            // Sort by priority (higher priority first)
            const sortedListeners = [...listeners].sort((a, b) => b.priority - a.priority);
            // Execute listeners
            for (const listener of sortedListeners) {
                try {
                    await listener.callback(...args);
                    // Remove if it's a one-time listener
                    if (listener.once) {
                        const index = listeners.indexOf(listener);
                        if (index !== -1) {
                            listeners.splice(index, 1);
                        }
                    }
                }
                catch (error) {
                    Logger_1.Logger.error(`Error in event listener for ${eventName}:`, error);
                }
            }
            Logger_1.Logger.debug(`Event emitted: ${eventName}`, { args });
        }
        catch (error) {
            Logger_1.Logger.error(`Error emitting event ${eventName}:`, error);
        }
    }
    /**
     * Add middleware
     */
    use(middleware) {
        this.middlewares.push(middleware);
        Logger_1.Logger.debug('Event middleware added');
    }
    /**
     * Remove middleware
     */
    removeMiddleware(middleware) {
        const index = this.middlewares.indexOf(middleware);
        if (index !== -1) {
            this.middlewares.splice(index, 1);
            Logger_1.Logger.debug('Event middleware removed');
        }
    }
    /**
     * Get event listeners count
     */
    getListenerCount(eventName) {
        const listeners = this.events.get(eventName);
        return listeners ? listeners.length : 0;
    }
    /**
     * Get all event names
     */
    getEventNames() {
        return Array.from(this.events.keys());
    }
    /**
     * Check if event has listeners
     */
    hasListeners(eventName) {
        return this.getListenerCount(eventName) > 0;
    }
    /**
     * Get event history
     */
    getEventHistory() {
        return [...this.eventHistory];
    }
    /**
     * Clear event history
     */
    clearEventHistory() {
        this.eventHistory = [];
        Logger_1.Logger.debug('Event history cleared');
    }
    /**
     * Wait for an event to be emitted
     */
    waitFor(eventName, timeout) {
        return new Promise((resolve, reject) => {
            let timeoutId;
            const listener = (...args) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                this.off(eventName, listener);
                resolve(args);
            };
            this.once(eventName, listener);
            if (timeout) {
                timeoutId = window.setTimeout(() => {
                    this.off(eventName, listener);
                    reject(new Error(`Event ${eventName} timeout after ${timeout}ms`));
                }, timeout);
            }
        });
    }
    /**
     * Create a namespaced event emitter
     */
    namespace(prefix) {
        return new NamespacedEventManager(this, prefix);
    }
    /**
     * Bind RAGE MP events to internal event system
     */
    bindRageEvent(rageName, internalName) {
        const eventName = internalName || rageName;
        mp.events.add(rageName, (...args) => {
            this.emit(eventName, ...args);
        });
        Logger_1.Logger.debug(`Bound RAGE event ${rageName} to internal event ${eventName}`);
    }
    /**
     * Unbind RAGE MP events
     */
    unbindRageEvent(rageName) {
        mp.events.remove(rageName);
        Logger_1.Logger.debug(`Unbound RAGE event: ${rageName}`);
    }
    /**
     * Add event listener with options
     */
    addEventListener(eventName, callback, once, priority) {
        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }
        const listeners = this.events.get(eventName);
        listeners.push({
            callback,
            once,
            priority
        });
        Logger_1.Logger.debug(`Event listener added: ${eventName}`, { once, priority });
    }
    /**
     * Apply middlewares
     */
    async applyMiddlewares(eventName, args) {
        for (const middleware of this.middlewares) {
            await new Promise((resolve, reject) => {
                try {
                    const next = () => resolve();
                    const result = middleware(eventName, args, next);
                    // Handle async middleware
                    if (result instanceof Promise) {
                        result.then(() => resolve()).catch(reject);
                    }
                }
                catch (error) {
                    reject(error);
                }
            });
        }
    }
    /**
     * Add event to history
     */
    addToHistory(eventName, args) {
        this.eventHistory.push({
            name: eventName,
            args: [...args],
            timestamp: new Date()
        });
        // Maintain history size limit
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }
    /**
     * Get event statistics
     */
    getEventStats() {
        const stats = {};
        this.eventHistory.forEach(event => {
            stats[event.name] = (stats[event.name] || 0) + 1;
        });
        return stats;
    }
}
exports.EventManager = EventManager;
/**
 * Namespaced Event Manager
 * Provides scoped event handling for client-side
 */
class NamespacedEventManager {
    constructor(eventManager, prefix) {
        this.eventManager = eventManager;
        this.prefix = prefix;
    }
    on(eventName, callback, priority = 0) {
        this.eventManager.on(`${this.prefix}:${eventName}`, callback, priority);
    }
    once(eventName, callback, priority = 0) {
        this.eventManager.once(`${this.prefix}:${eventName}`, callback, priority);
    }
    off(eventName, callback) {
        this.eventManager.off(`${this.prefix}:${eventName}`, callback);
    }
    async emit(eventName, ...args) {
        await this.eventManager.emit(`${this.prefix}:${eventName}`, ...args);
    }
    getListenerCount(eventName) {
        return this.eventManager.getListenerCount(`${this.prefix}:${eventName}`);
    }
    hasListeners(eventName) {
        return this.eventManager.hasListeners(`${this.prefix}:${eventName}`);
    }
    waitFor(eventName, timeout) {
        return this.eventManager.waitFor(`${this.prefix}:${eventName}`, timeout);
    }
}
exports.NamespacedEventManager = NamespacedEventManager;
//# sourceMappingURL=EventManager.js.map